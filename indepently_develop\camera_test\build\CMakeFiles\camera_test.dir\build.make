# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /media/sf_work/auto_sort_high_efficient/camera_test

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /media/sf_work/auto_sort_high_efficient/camera_test/build

# Include any dependencies generated for this target.
include CMakeFiles/camera_test.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/camera_test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/camera_test.dir/flags.make

CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: CMakeFiles/camera_test.dir/flags.make
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../berxel_camera_test.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o"
	/usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o -c /media/sf_work/auto_sort_high_efficient/camera_test/berxel_camera_test.cpp

CMakeFiles/camera_test.dir/berxel_camera_test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/camera_test.dir/berxel_camera_test.cpp.i"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /media/sf_work/auto_sort_high_efficient/camera_test/berxel_camera_test.cpp > CMakeFiles/camera_test.dir/berxel_camera_test.cpp.i

CMakeFiles/camera_test.dir/berxel_camera_test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/camera_test.dir/berxel_camera_test.cpp.s"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /media/sf_work/auto_sort_high_efficient/camera_test/berxel_camera_test.cpp -o CMakeFiles/camera_test.dir/berxel_camera_test.cpp.s

CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o.requires:

.PHONY : CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o.requires

CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o.provides: CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o.requires
	$(MAKE) -f CMakeFiles/camera_test.dir/build.make CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o.provides.build
.PHONY : CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o.provides

CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o.provides.build: CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o


CMakeFiles/camera_test.dir/main.cpp.o: CMakeFiles/camera_test.dir/flags.make
CMakeFiles/camera_test.dir/main.cpp.o: ../main.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/camera_test.dir/main.cpp.o"
	/usr/bin/g++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/camera_test.dir/main.cpp.o -c /media/sf_work/auto_sort_high_efficient/camera_test/main.cpp

CMakeFiles/camera_test.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/camera_test.dir/main.cpp.i"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /media/sf_work/auto_sort_high_efficient/camera_test/main.cpp > CMakeFiles/camera_test.dir/main.cpp.i

CMakeFiles/camera_test.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/camera_test.dir/main.cpp.s"
	/usr/bin/g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /media/sf_work/auto_sort_high_efficient/camera_test/main.cpp -o CMakeFiles/camera_test.dir/main.cpp.s

CMakeFiles/camera_test.dir/main.cpp.o.requires:

.PHONY : CMakeFiles/camera_test.dir/main.cpp.o.requires

CMakeFiles/camera_test.dir/main.cpp.o.provides: CMakeFiles/camera_test.dir/main.cpp.o.requires
	$(MAKE) -f CMakeFiles/camera_test.dir/build.make CMakeFiles/camera_test.dir/main.cpp.o.provides.build
.PHONY : CMakeFiles/camera_test.dir/main.cpp.o.provides

CMakeFiles/camera_test.dir/main.cpp.o.provides.build: CMakeFiles/camera_test.dir/main.cpp.o


# Object files for target camera_test
camera_test_OBJECTS = \
"CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o" \
"CMakeFiles/camera_test.dir/main.cpp.o"

# External object files for target camera_test
camera_test_EXTERNAL_OBJECTS =

camera_test: CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o
camera_test: CMakeFiles/camera_test.dir/main.cpp.o
camera_test: CMakeFiles/camera_test.dir/build.make
camera_test: CMakeFiles/camera_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable camera_test"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/camera_test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/camera_test.dir/build: camera_test

.PHONY : CMakeFiles/camera_test.dir/build

CMakeFiles/camera_test.dir/requires: CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o.requires
CMakeFiles/camera_test.dir/requires: CMakeFiles/camera_test.dir/main.cpp.o.requires

.PHONY : CMakeFiles/camera_test.dir/requires

CMakeFiles/camera_test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/camera_test.dir/cmake_clean.cmake
.PHONY : CMakeFiles/camera_test.dir/clean

CMakeFiles/camera_test.dir/depend:
	cd /media/sf_work/auto_sort_high_efficient/camera_test/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /media/sf_work/auto_sort_high_efficient/camera_test /media/sf_work/auto_sort_high_efficient/camera_test /media/sf_work/auto_sort_high_efficient/camera_test/build /media/sf_work/auto_sort_high_efficient/camera_test/build /media/sf_work/auto_sort_high_efficient/camera_test/build/CMakeFiles/camera_test.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/camera_test.dir/depend


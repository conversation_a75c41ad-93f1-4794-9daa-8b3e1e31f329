# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: install/local

.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: install/strip

.PHONY : install/strip/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/build/CMakeFiles /media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/build/CMakeFiles/progress.marks
	$(MAKE) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named compete_manage_example

# Build rule for target.
compete_manage_example: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 compete_manage_example
.PHONY : compete_manage_example

# fast build rule for target.
compete_manage_example/fast:
	$(MAKE) -f CMakeFiles/compete_manage_example.dir/build.make CMakeFiles/compete_manage_example.dir/build
.PHONY : compete_manage_example/fast

compete_manage.o: compete_manage.cpp.o

.PHONY : compete_manage.o

# target to build an object file
compete_manage.cpp.o:
	$(MAKE) -f CMakeFiles/compete_manage_example.dir/build.make CMakeFiles/compete_manage_example.dir/compete_manage.cpp.o
.PHONY : compete_manage.cpp.o

compete_manage.i: compete_manage.cpp.i

.PHONY : compete_manage.i

# target to preprocess a source file
compete_manage.cpp.i:
	$(MAKE) -f CMakeFiles/compete_manage_example.dir/build.make CMakeFiles/compete_manage_example.dir/compete_manage.cpp.i
.PHONY : compete_manage.cpp.i

compete_manage.s: compete_manage.cpp.s

.PHONY : compete_manage.s

# target to generate assembly for a file
compete_manage.cpp.s:
	$(MAKE) -f CMakeFiles/compete_manage_example.dir/build.make CMakeFiles/compete_manage_example.dir/compete_manage.cpp.s
.PHONY : compete_manage.cpp.s

example.o: example.cpp.o

.PHONY : example.o

# target to build an object file
example.cpp.o:
	$(MAKE) -f CMakeFiles/compete_manage_example.dir/build.make CMakeFiles/compete_manage_example.dir/example.cpp.o
.PHONY : example.cpp.o

example.i: example.cpp.i

.PHONY : example.i

# target to preprocess a source file
example.cpp.i:
	$(MAKE) -f CMakeFiles/compete_manage_example.dir/build.make CMakeFiles/compete_manage_example.dir/example.cpp.i
.PHONY : example.cpp.i

example.s: example.cpp.s

.PHONY : example.s

# target to generate assembly for a file
example.cpp.s:
	$(MAKE) -f CMakeFiles/compete_manage_example.dir/build.make CMakeFiles/compete_manage_example.dir/example.cpp.s
.PHONY : example.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... compete_manage_example"
	@echo "... edit_cache"
	@echo "... compete_manage.o"
	@echo "... compete_manage.i"
	@echo "... compete_manage.s"
	@echo "... example.o"
	@echo "... example.i"
	@echo "... example.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system


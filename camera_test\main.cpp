/**
 * @file main.cpp
 * @brief Berxel相机测试程序主文件
 * @details 演示如何使用BerxelCameraTest类进行图像数据读取和灰度算法测试
 * <AUTHOR> from original camera_drive_berxel
 * @date 2025-08-26
 * @version v1.0.0
 */

#include "berxel_camera_test.hpp"
#include <iostream>
#include <chrono>
#include <thread>
#include <signal.h>
#include <cmath>
#include <iomanip>
#include <fstream>
#include <sstream>

// 全局变量用于信号处理
static bool g_running = true;
static BerxelCameraTest* g_camera = nullptr;

// 初始化spdlog文件日志
void init_logger() {
    try {
        // 创建旋转文件日志，每个文件最大5MB，最多保留3个文件
        auto rotating_logger = spdlog::rotating_logger_mt("camera_logger", "logs/camera_test.log", 1024 * 1024 * 5, 3);
        
        // 设置日志格式：[时间] [日志级别] [线程ID] 消息
        rotating_logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%l] [%t] %v");
        
        // 设置日志级别为info
        rotating_logger->set_level(spdlog::level::info);
        
        // 设置为默认日志器
        spdlog::set_default_logger(rotating_logger);
        
        // 设置自动刷新
        spdlog::flush_every(std::chrono::seconds(3));
        
        spdlog::info("日志系统初始化成功");
        
    } catch (const spdlog::spdlog_ex& ex) {
        spdlog::error("日志初始化失败: {}", ex.what());
    }
}

// 信号处理函数</search>
void signal_handler(int signal) {
    std::cout << "\n接收到信号 " << signal << "，正在退出..." << std::endl;
    g_running = false;
    if (g_camera) {
        g_camera->stop();
    }
}

// 全局相机对象指针，用于在回调函数中访问检测方法
static BerxelCameraTest* g_camera_instance = nullptr;

// 自定义帧回调函数 - 使用BerxelCameraTest类的灰度物品检测算法
void custom_frame_callback(berxel::BerxelHawkStreamType streamType,
                        berxel::BerxelHawkFrame* pFrame,
                        void* pUserData) {
    static int callback_count = 0;
    callback_count++;
    
    // std::cout << "\n=== 帧回调函数被调用 #" << callback_count << " ===" << std::endl;
    // std::cout << "流类型: " << streamType << std::endl;
    // std::cout << "帧尺寸: " << pFrame->getWidth() << "x" << pFrame->getHeight() << std::endl;
    // std::cout << "帧索引: " << pFrame->getFrameIndex() << std::endl;
    // std::cout << "数据大小: " << pFrame->getDataSize() << " bytes" << std::endl;
    
    // 使用BerxelCameraTest类的灰度物品检测算法 - 已暂时屏蔽
    /*
    if (streamType == berxel::BERXEL_HAWK_COLOR_STREAM && g_camera_instance) {
        gray_detection_result result = g_camera_instance->detect_gray_object(pFrame);
        
        std::cout << "=== 灰度物品检测结果 ===" << std::endl;
        std::cout << "检测结果: " << (result.object_detected ? "发现物品" : "无物品") << std::endl;
        std::cout << "检测面积: " << result.detected_area << " 像素" << std::endl;
        std::cout << "背景灰度: " << (int)result.background_gray << std::endl;
        std::cout << "检测阈值: " << (int)result.detection_threshold << std::endl;
        std::cout << "帧索引: " << result.frame_index << std::endl;
        
        // 可以在这里添加检测结果的处理逻辑
        if (result.object_detected) {
            std::cout << ">>> 灰度物品检测成功！触发后续处理逻辑 <<<" << std::endl;
            // 这里可以添加物品检测成功后的处理代码
            // 例如：保存图像、发送信号、记录日志等
        }
    } else */ {
        // 深度检测功能已移动到image_processing_thread中进行
        // 这里只保留简单的帧信息记录（可选）
        // std::cout << "接收到帧数据，流类型: " << streamType << "，帧索引: " << pFrame->getFrameIndex() << std::endl;
    }
    
    std::cout << "=== 帧处理完成 ===" << std::endl;
}

// // 深度图像处理示例函数
// void process_depth_image(const uint16_t* depth_data, int width, int height) {
//     if (!depth_data) return;
    
//     // 统计深度值分布
//     uint16_t min_depth = 65535, max_depth = 0;
//     int valid_pixels = 0;
//     uint64_t sum = 0;
    
//     for (int i = 0; i < width * height; i++) {
//         if (depth_data[i] > 0) {  // 忽略无效深度值
//             if (depth_data[i] < min_depth) min_depth = depth_data[i];
//             if (depth_data[i] > max_depth) max_depth = depth_data[i];
//             sum += depth_data[i];
//             valid_pixels++;
//         }
//     }
    
//     if (valid_pixels > 0) {
//         double average_depth = static_cast<double>(sum) / valid_pixels;
//         std::cout << "深度图像统计信息:" << std::endl;
//         std::cout << "  有效像素数: " << valid_pixels << "/" << (width * height) << std::endl;
//         std::cout << "  深度范围: " << min_depth << " - " << max_depth << " mm" << std::endl;
//         std::cout << "  平均深度: " << average_depth << " mm" << std::endl;
        
//         // 统计深度直方图（简化版，分成10个区间）
//         int bins = 10;
//         std::vector<int> histogram(bins, 0);
//         int depth_range = max_depth - min_depth;
        
//         if (depth_range > 0) {
//             for (int i = 0; i < width * height; i++) {
//                 if (depth_data[i] > 0) {
//                     int bin_index = ((depth_data[i] - min_depth) * (bins - 1)) / depth_range;
//                     if (bin_index >= 0 && bin_index < bins) {
//                         histogram[bin_index]++;
//                     }
//                 }
//             }
            
//             std::cout << "  深度分布直方图:" << std::endl;
//             for (int i = 0; i < bins; i++) {
//                 int range_start = min_depth + (i * depth_range) / bins;
//                 int range_end = min_depth + ((i + 1) * depth_range) / bins;
//                 std::cout << "    [" << range_start << "-" << range_end << "mm]: " 
//                           << histogram[i] << " 像素" << std::endl;
//             }
//         }
//     } else {
//         std::cout << "深度图像中没有有效的深度数据" << std::endl;
//     }
// }

// // 灰度图像处理示例函数
// void process_gray_image(const uint8_t* gray_data, int width, int height) {
//     if (!gray_data) return;
    
//     // 示例：计算图像的平均灰度值
//     uint64_t sum = 0;
//     for (int i = 0; i < width * height; i++) {
//         sum += gray_data[i];
//     }
//     double average = static_cast<double>(sum) / (width * height);
    
//     std::cout << "图像平均灰度值: " << average << std::endl;
    
//     // 示例：统计灰度直方图
//     int histogram[256] = {0};
//     for (int i = 0; i < width * height; i++) {
//         histogram[gray_data[i]]++;
//     }
    
//     // 找到最频繁的灰度值
//     int max_count = 0;
//     int most_frequent_gray = 0;
//     for (int i = 0; i < 256; i++) {
//         if (histogram[i] > max_count) {
//             max_count = histogram[i];
//             most_frequent_gray = i;
//         }
//     }
    
//     std::cout << "最频繁的灰度值: " << most_frequent_gray 
//               << " (出现次数: " << max_count << ")" << std::endl;
// }

// 边缘检测示例（简单的Sobel算子）
void edge_detection(const uint8_t* input, uint8_t* output, int width, int height) {
    if (!input || !output) return;
    
    // Sobel算子
    int sobel_x[3][3] = {{-1, 0, 1}, {-2, 0, 2}, {-1, 0, 1}};
    int sobel_y[3][3] = {{-1, -2, -1}, {0, 0, 0}, {1, 2, 1}};
    
    for (int y = 1; y < height - 1; y++) {
        for (int x = 1; x < width - 1; x++) {
            int gx = 0, gy = 0;
            
            // 应用Sobel算子
            for (int ky = -1; ky <= 1; ky++) {
                for (int kx = -1; kx <= 1; kx++) {
                    int pixel = input[(y + ky) * width + (x + kx)];
                    gx += pixel * sobel_x[ky + 1][kx + 1];
                    gy += pixel * sobel_y[ky + 1][kx + 1];
                }
            }
            
            // 计算梯度幅值
            int magnitude = static_cast<int>(sqrt(gx * gx + gy * gy));
            output[y * width + x] = (magnitude > 255) ? 255 : magnitude;
        }
    }
}

// 配置灰度物品检测参数
void configure_gray_detection(BerxelCameraTest* camera) {
    std::cout << "\n=== 配置灰度物品检测参数 ===" << std::endl;
    
    // 定义检测区域坐标
    point_pos_2d detection_coords[4];

    // detection_coords[0] = {185, 245};  // 左上角
    // detection_coords[1] = {430, 245};  // 右上角
    // detection_coords[2] = {185, 120};  // 左下角
    // detection_coords[3] = {430, 120};  // 右下角

    detection_coords[0] = {206, 100};  // 左上角     254  363 442 567
    detection_coords[1] = {343, 72};  // 右上角    98  234 379 722 
    detection_coords[2] = {386, 274};  // 左下角
    detection_coords[3] = {247, 299};  // 右下角

    
    uint8_t gray_mask = 50;      // 灰度阈值偏移
    uint32_t area_mask = 50;    // 最小检测面积
    
    // 配置相机的检测参数
    camera->configure_gray_detection(detection_coords, gray_mask, area_mask);
    
    std::cout << "=== 配置完成 ===" << std::endl;
}

// 配置深度物品检测参数
void configure_depth_detection(BerxelCameraTest* camera) {
    std::cout << "\n=== 配置深度物品检测参数 ===" << std::endl;
    
    // 定义检测区域坐标（与灰度检测区域相同）
    point_pos_2d detection_coords[4];

    // detection_coords[0] = {185, 245};  // 左上角
    // detection_coords[1] = {430, 245};  // 右上角
    // detection_coords[2] = {185, 120};  // 左下角
    // detection_coords[3] = {430, 120};  // 右下角

    detection_coords[0] = {206, 100};  // 左上角     254  363 442 567
    detection_coords[1] = {343, 72};  // 右上角    98  234 379 722 
    detection_coords[2] = {386, 274};  // 左下角
    detection_coords[3] = {274, 299};  // 右下角
    
    uint16_t depth_threshold = 90;   // 深度阈值（毫米*10）
    uint32_t area_mask = 3;       // 最小检测面积
    
    // 配置相机的深度检测参数
    camera->configure_depth_detection(detection_coords, depth_threshold, area_mask);
    
    std::cout << "=== 配置完成 ===" << std::endl;
}

int main(int argc, char* argv[]) {
    // 初始化日志系统
    init_logger();
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 创建相机测试对象
    spdlog::info("开始创建相机测试对象");
    BerxelCameraTest camera;
    g_camera = &camera;
    g_camera_instance = &camera;  // 设置全局相机实例指针
    spdlog::info("相机测试对象创建成功");
    
    // 配置灰度物品检测参数 - 已暂时屏蔽
    configure_gray_detection(&camera);
    
    // 配置深度物品检测参数
    configure_depth_detection(&camera);
    
    // 设置自定义帧回调函数
    camera.set_frame_callback(custom_frame_callback);
    
    // 初始化相机
    if (!camera.init()) {
        std::cerr << "相机初始化失败！" << std::endl;
        return -1;
    }
    
    // 启动相机流
    if (!camera.start()) {
        std::cerr << "启动相机流失败！" << std::endl;
        return -1;
    }
    
    std::cout << "\n相机已启动,开始采集图像数据..." << std::endl;
    std::cout << "按 Ctrl+C 退出程序" << std::endl;
    
    // 主循环
    int frame_save_counter = 0;
    auto last_save_time = std::chrono::steady_clock::now();
    
    // 用于保存彩色图像数据的静态变量
    static std::vector<RGB888> saved_color_data;
    static int saved_color_width = 0;
    static int saved_color_height = 0;
    
    while (g_running) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        // 获取相机信息
        std::string camera_info;
        if (camera.get_camera_info(camera_info)) {
            // 每5秒打印一次相机信息
            static auto last_info_time = std::chrono::steady_clock::now();
            auto now = std::chrono::steady_clock::now();
            if (std::chrono::duration_cast<std::chrono::seconds>(now - last_info_time).count() >= 100) {
                std::cout << "\n" << camera_info << std::endl;
                last_info_time = now;
            }
        }
        
        // 获取彩色图像数据
        int color_width, color_height;
        if (camera.get_color_image(nullptr, color_width, color_height)) {
            std::vector<RGB888> color_data(color_width * color_height);
            if (camera.get_color_image(color_data.data(), color_width, color_height)) {
                // 处理彩色图像
                // process_color_image(color_data.data(), color_width, color_height);
                
                // 保存彩色图像数据供后续使用
                saved_color_data = color_data;
                saved_color_width = color_width;
                saved_color_height = color_height;
            }
        }
        
        // 获取灰度图像数据
        int width, height;
        if (camera.get_gray_image(nullptr, width, height)) {
            std::vector<uint8_t> gray_data(width * height);
            if (camera.get_gray_image(gray_data.data(), width, height)) {
                // 处理灰度图像
                // process_gray_image(gray_data.data(), width, height);
                
                // 每10秒保存一次所有图像
                auto now = std::chrono::steady_clock::now();
                if (std::chrono::duration_cast<std::chrono::seconds>(now - last_save_time).count() >= 100) {
                    // // 保存原始彩色图像 - 暂时屏蔽
                    // std::string color_filename = "color_image_" + std::to_string(frame_save_counter) + ".bmp";
                    // if (!saved_color_data.empty()) {
                    //     camera.save_color_image_bmp(color_filename, saved_color_data.data(), saved_color_width, saved_color_height);
                    //
                    //     // // 保存高斯模糊处理后的彩色图像（轻度模糊）
                    //     // std::string blur_light_filename = "color_blur_light_" + std::to_string(frame_save_counter) + ".bmp";
                    //     // camera.save_gaussian_blurred_color_image_bmp(blur_light_filename, saved_color_data.data(),
                    //     //                                            saved_color_width, saved_color_height, 1.0, 5);
                    //
                    //     // // 保存高斯模糊处理后的彩色图像（中度模糊）
                    //     // std::string blur_medium_filename = "color_blur_medium_" + std::to_string(frame_save_counter) + ".bmp";
                    //     // camera.save_gaussian_blurred_color_image_bmp(blur_medium_filename, saved_color_data.data(),
                    //     //                                            saved_color_width, saved_color_height, 2.5, 9);
                    //
                    //     // // 保存高斯模糊处理后的彩色图像（重度模糊）
                    //     // std::string blur_heavy_filename = "color_blur_heavy_" + std::to_string(frame_save_counter) + ".bmp";
                    //     // camera.save_gaussian_blurred_color_image_bmp(blur_heavy_filename, saved_color_data.data(),
                    //     //                                            saved_color_width, saved_color_height, 5.0, 15);
                    // }
                    
                    // // 保存原始灰度图像 - 暂时屏蔽
                    // std::string gray_filename = "gray_image_" + std::to_string(frame_save_counter) + ".bmp";
                    // camera.save_gray_image_bmp(gray_filename, gray_data.data(), width, height);
                    
                    // 边缘检测示例
                    std::vector<uint8_t> edge_data(width * height, 0);
                    edge_detection(gray_data.data(), edge_data.data(), width, height);
                    
                    // 保存边缘检测结果
                    std::string edge_filename = "edge_image_" + std::to_string(frame_save_counter) + ".bmp";
                    camera.save_gray_image_bmp(edge_filename, edge_data.data(), width, height);
                    
                    // 获取并保存深度图像数据
                    int depth_width, depth_height;
                    if (camera.get_depth_image(nullptr, depth_width, depth_height)) {
                        std::vector<uint16_t> depth_data(depth_width * depth_height);
                        if (camera.get_depth_image(depth_data.data(), depth_width, depth_height)) {
                            // 处理深度图像数据，显示统计信息
                            // process_depth_image(depth_data.data(), depth_width, depth_height);
                            
                            // // 保存原始深度图像 - 暂时屏蔽
                            // std::string depth_filename = "depth_image_" + std::to_string(frame_save_counter) + ".bmp";
                            // camera.save_depth_image_bmp(depth_filename, depth_data.data(), depth_width, depth_height);
                            
                            // // 保存直方图均衡化后的深度图像 - 暂时屏蔽
                            // std::string hist_eq_filename = "depth_hist_eq_" + std::to_string(frame_save_counter) + ".bmp";
                            // camera.save_hist_equalized_depth_image_bmp(hist_eq_filename, depth_data.data(), depth_width, depth_height);
                            
                            // // 将RGB图像转换为HSV图像并保存 - 暂时屏蔽
                            // if (!saved_color_data.empty()) {
                            //     // 创建HSV数据缓冲区
                            //     std::vector<HSV> hsv_data(saved_color_width * saved_color_height);
                            //
                            //     // 执行RGB到HSV的转换
                            //     camera.convert_rgb_to_hsv(saved_color_data.data(), hsv_data.data(),
                            //                             saved_color_width, saved_color_height);
                            //
                            //     // 保存HSV图像
                            //     std::string hsv_filename = "hsv_image_" + std::to_string(frame_save_counter) + ".bmp";
                            //     camera.save_hsv_image_bmp(hsv_filename, hsv_data.data(),
                            //                             saved_color_width, saved_color_height);
                            //
                            //     std::cout << "已保存图像文件: " << color_filename << ", " << gray_filename << ", " << edge_filename
                            //               << ", " << depth_filename << ", " << hist_eq_filename << ", " << hsv_filename;
                            // } else {
                            //     std::cout << "已保存图像文件: " << color_filename << ", " << gray_filename << ", " << edge_filename
                            //               << ", " << depth_filename << ", " << hist_eq_filename;
                            // }
                            
                            // 只保存边缘检测图像
                            std::cout << "已保存图像文件: " << edge_filename;
                            // if (!saved_color_data.empty()) {
                            //     std::cout << ", color_blur_light_" << frame_save_counter << ".bmp"
                            //               << ", color_blur_medium_" << frame_save_counter << ".bmp"
                            //               << ", color_blur_heavy_" << frame_save_counter << ".bmp";
                            // }
                            std::cout << std::endl;
                        } else {
                            std::cout << "已保存图像文件: " << edge_filename << " (深度数据获取失败)" << std::endl;
                        }
                    } else {
                        std::cout << "已保存图像文件: " << edge_filename << " (深度数据不可用)" << std::endl;
                    }
                    
                    frame_save_counter++;
                    last_save_time = now;
                }
            }
        }
        
        
    }
    
    std::cout << "\n正在停止相机..." << std::endl;
    camera.stop();
    
    std::cout << "程序已退出。" << std::endl;
    return 0;
}
# Berxel相机测试程序

## 概述

这是一个从原始 `scheduler_hori_high_efficient/gray_camera_manager/` 中提取的独立 Berxel 相机测试程序。该程序专门用于灰度算法开发和测试，提供了简洁的 API 来读取相机图像数据。

## 功能特性

- **相机初始化和控制**: 自动检测和初始化 Berxel 相机设备
- **实时图像采集**: 支持彩色和灰度图像数据获取
- **灰度转换**: 自动将 RGB 图像转换为灰度图像
- **图像保存**: 支持保存 BMP 格式的图像文件
- **算法测试框架**: 提供示例算法（边缘检测、直方图统计等）
- **回调机制**: 支持自定义帧处理回调函数

## 文件结构

```
camera_test/
├── CMakeLists.txt              # CMake 构建配置
├── berxel_camera_test.hpp      # 相机测试类头文件
├── berxel_camera_test.cpp      # 相机测试类实现
├── main.cpp                    # 主程序文件
└── README.md                   # 本说明文件
```

## 依赖项

### 系统依赖
- Linux 操作系统
- GCC 编译器 (支持 C++11)
- CMake 3.5 或更高版本

### 库依赖
- **BerxelHawk SDK**: Berxel 相机 SDK 库
- **spdlog**: 日志库（可选，用于调试）

### 库文件位置
- 头文件: `../share/libs/include/` 或 `../share/libs/x86/include/`
- 库文件: `../share/libs/lib/` 或 `../share/libs/x86/lib/`

## 编译方法

### 1. 进入项目目录
```bash
cd camera_test
```

### 2. 创建构建目录
```bash
mkdir build
cd build
```

### 3. 配置和编译
```bash
cmake ..
make
```

### 4. 运行程序
```bash
./camera_test
```

## 使用方法

### 基本使用

```cpp
#include "berxel_camera_test.hpp"

int main() {
    // 创建相机对象
    BerxelCameraTest camera;
    
    // 初始化相机
    if (!camera.init()) {
        std::cerr << "相机初始化失败" << std::endl;
        return -1;
    }
    
    // 启动相机流
    if (!camera.start()) {
        std::cerr << "启动相机流失败" << std::endl;
        return -1;
    }
    
    // 获取图像数据
    int width, height;
    std::vector<uint8_t> gray_data;
    
    if (camera.getGrayImage(nullptr, width, height)) {
        gray_data.resize(width * height);
        camera.getGrayImage(gray_data.data(), width, height);
        
        // 处理灰度图像数据
        // ... 你的算法代码 ...
    }
    
    // 停止相机
    camera.stop();
    return 0;
}
```

### 自定义回调函数

```cpp
void myFrameCallback(berxel::BerxelHawkStreamType streamType, 
                    berxel::BerxelHawkFrame* pFrame, 
                    void* pUserData) {
    // 自定义帧处理逻辑
    if (streamType == berxel::BERXEL_HAWK_COLOR_STREAM) {
        // 处理彩色帧
    }
}

// 设置回调函数
camera.setFrameCallback(myFrameCallback);
```

## API 参考

### BerxelCameraTest 类

#### 主要方法

- `bool init()`: 初始化相机设备
- `bool start()`: 启动相机数据流
- `void stop()`: 停止相机数据流
- `bool getGrayImage(uint8_t* data, int& width, int& height)`: 获取灰度图像
- `bool getColorImage(RGB888* data, int& width, int& height)`: 获取彩色图像
- `bool saveGrayImageBMP(const std::string& filename, const uint8_t* data, int width, int height)`: 保存灰度BMP图像
- `bool saveColorImageBMP(const std::string& filename, const RGB888* data, int width, int height)`: 保存彩色BMP图像
- `void setFrameCallback(FrameCallback callback)`: 设置帧回调函数

#### 静态方法

- `static uint8_t rgbToGray(const RGB888& rgb)`: RGB 转灰度

## 示例算法

程序包含以下示例算法：

### 1. 灰度统计
- 计算平均灰度值
- 生成灰度直方图
- 查找最频繁的灰度值

### 2. 边缘检测
- 使用 Sobel 算子进行边缘检测
- 输出边缘强度图像

### 3. 图像保存
- 支持 BMP 格式（灰度图像，8位带调色板）
- 支持 BMP 格式（彩色图像，24位真彩色）

## 配置参数

可以通过修改 `BerxelCameraTest` 构造函数中的默认配置来调整相机参数：

```cpp
// 帧率设置
m_config.frame_rate = 10;

// 图像尺寸
m_frame_width = 640;
m_frame_height = 400;

// 曝光参数
m_config.ae_para.auto_ae = false;
m_config.ae_para.exposure_time = 10000;
m_config.ae_para.gain = 3502;
```

## 故障排除

### 常见问题

1. **找不到设备**
   - 确保 Berxel 相机已正确连接
   - 检查 USB 权限设置
   - 验证驱动程序是否正确安装

2. **编译错误**
   - 检查 BerxelHawk SDK 是否正确安装
   - 验证库文件路径是否正确
   - 确保编译器支持 C++11

3. **运行时错误**
   - 检查日志输出获取详细错误信息
   - 确保有足够的系统权限访问相机设备

### 调试建议

- 启用详细日志输出
- 使用 `getCameraInfo()` 方法获取设备信息
- 检查帧回调函数是否被正确调用

## 扩展开发

### 添加新算法

1. 在 `main.cpp` 中添加新的处理函数
2. 在主循环中调用你的算法
3. 使用 `getGrayImage()` 或 `getColorImage()` 获取图像数据

### 性能优化

- 使用多线程处理图像数据
- 实现图像数据的缓存机制
- 优化算法的内存使用

## 版本历史

- **v1.0.0** (2025-08-26): 初始版本，提取自原始相机管理模块

## 许可证

本程序基于原始项目的许可证条款。

## 联系方式

如有问题或建议，请联系开发团队。
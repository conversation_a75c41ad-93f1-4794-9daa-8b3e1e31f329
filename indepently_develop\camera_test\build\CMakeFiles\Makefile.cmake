# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "../CMakeLists.txt"
  "CMakeFiles/3.5.1/CMakeCCompiler.cmake"
  "CMakeFiles/3.5.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.5.1/CMakeSystem.cmake"
  "/usr/share/cmake-3.5/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake-3.5/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.5/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.5/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.5/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.5/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.5/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.5/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake-3.5/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake-3.5/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.5/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/share/cmake-3.5/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake-3.5/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake-3.5/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.5/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/camera_test.dir/DependInfo.cmake"
  )

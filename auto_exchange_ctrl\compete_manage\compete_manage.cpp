/**@file  	   compete_manage.cpp
* @brief       取换箱设备X轴防碰撞管理器实现
* @details     实现取换箱设备在X轴上的防碰撞功能，包括位置检测、轨道锁定、最近可达位置计算等
* <AUTHOR>
* @date        2025-01-05
* @version     v1.0.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.1.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2025/01/05  <td>1.0.0    <td>MLC       <td>初始版本	                   </tr>
* </table>
*
**********************************************************************************
*/

#include "compete_manage.hpp"
#include <iostream>
#include <spdlog/spdlog.h>

/**
 * @brief 初始化函数
 * @details 用于初始化防碰撞管理器的相关参数和数据结构
 */
void compete_manage::compete_manage_init()
{
    m_x_track_lock_map.clear();
    m_exchange_dev_current_pos_map.clear();
    
    m_slot_width = 10;
    m_safety_distance_slot_count = 1;   // 默认安全距离格口数量为1
    m_x_track_total_length = 5000;      // 默认X轨道总长度5000mm
}

/**
 * @brief X轨道位置锁定函数
 * @details 参考map_manager_dev_self_pos_lock实现，为交换设备在X轨道上锁定位置
 * @param[in] exchange_dev_id 交换设备ID
 * @param[in] x_pos X轨道位置
 */
void compete_manage::compete_manage_x_track_release(int exchange_dev_id, int x_pos)
{
    x_track_lock_info lock_info;
    // std::lock_guard<std::mutex> lock(m_x_track_lock_mutex);
    
    // 使用全局安全距离格口数量参数计算安全距离
    int safety_distance = m_safety_distance_slot_count * m_slot_width;
    
    lock_info.exchange_dev_id = exchange_dev_id;
    lock_info.lock_start_pos = x_pos - m_slot_width - safety_distance;
    lock_info.lock_end_pos = x_pos + m_slot_width + safety_distance;
    lock_info.safety_distance = safety_distance;
    
    if (m_x_track_lock_map.find(exchange_dev_id) == m_x_track_lock_map.end())
    {
        m_x_track_lock_map.insert(std::make_pair(exchange_dev_id, lock_info));
        SPDLOG_INFO("X-track lock: Add new device {} lock info, pos {}, lock range [{}, {}]",
                   exchange_dev_id, x_pos, lock_info.lock_start_pos, lock_info.lock_end_pos);
    }
    else
    {
        m_x_track_lock_map.at(exchange_dev_id) = lock_info;
        SPDLOG_INFO("X-track lock: Update device {} lock info, pos {}, lock range [{}, {}]",
                   exchange_dev_id, x_pos, lock_info.lock_start_pos, lock_info.lock_end_pos);
    }
    
}
/**
 * @brief 根据移动方向和竞争格口状态锁定X轨道位置函数
 * @details 根据设备的移动方向和是否为竞争格口来锁定X轨道目标位置，智能计算安全距离
 * @param[in] exchange_dev_id 交换设备ID
 * @param[in] target_pos X轨道目标位置
 * @param[in] direction 移动方向，1表示正向（增大方向），-1表示反向（减小方向），0表示静止
 * @param[in] is_compete_slot 是否为竞争格口，true时使用竞争格口安全距离，false时使用普通安全距离
 */
void compete_manage::compete_manage_x_track_lock_by_direction(int exchange_dev_id, int target_pos, int direction, bool is_compete_slot)
{
    // std::lock_guard<std::mutex> lock(m_x_track_lock_mutex);
    
    // 检查方向有效性
    if (direction == 0)
    {
        SPDLOG_WARN("X-track lock: Invalid direction {} for device {}, lock operation ignored", direction, exchange_dev_id);
        return;
    }
    
    // 从容器中查找现有锁定信息
    auto it = m_x_track_lock_map.find(exchange_dev_id);
    x_track_lock_info lock_info;
    
    if (it != m_x_track_lock_map.end())
    {
        // 找到现有信息，复制现有数据作为基础
        lock_info = it->second;
        SPDLOG_DEBUG("X-track lock: Found existing lock info for device {}", exchange_dev_id);
    }
    else
    {
        // 没有找到现有信息，初始化新的锁定信息
        lock_info.exchange_dev_id = exchange_dev_id;
        SPDLOG_DEBUG("X-track lock: Creating new lock info for device {}", exchange_dev_id);
    }
    
    // 根据是否为竞争格口计算安全距离
    int safety_distance;
    if (is_compete_slot)
    {
        // 竞争格口使用两倍的安全距离格口数量
        safety_distance = (m_safety_distance_slot_count + 2) * m_slot_width;
    }
    else
    {
        // 普通情况使用标准安全距离格口数量
        safety_distance = (m_safety_distance_slot_count + 1) * m_slot_width;
    }
    
    // 修改需要更新的锁定信息
    lock_info.safety_distance = safety_distance;
    
    if (direction > 0) // 正向移动
    {
        // lock_info.lock_start_pos = target_pos;
        lock_info.lock_end_pos = target_pos + safety_distance;
    }
    else if (direction < 0) // 反向移动
    {
        lock_info.lock_start_pos = target_pos - safety_distance;
        // lock_info.lock_end_pos = target_pos;
    }
    
    // 确保锁定范围不超出轨道边界
    if (lock_info.lock_start_pos < 0)
        lock_info.lock_start_pos = 0;
    if (lock_info.lock_end_pos > m_x_track_total_length)
        lock_info.lock_end_pos = m_x_track_total_length;
    
    // 将修改后的信息更新到容器中
    m_x_track_lock_map[exchange_dev_id] = lock_info;
    
    // 输出设备锁定信息日志
    SPDLOG_INFO("X-track lock: {} device {} lock info, target pos {}, direction {}, compete_slot {}, lock range [{}, {}]",
               (it != m_x_track_lock_map.end()) ? "Updated" : "Added",
               exchange_dev_id, target_pos, direction, is_compete_slot, lock_info.lock_start_pos, lock_info.lock_end_pos);
}
/**
 * @brief 删除设备位置信息函数
 * @details 删除指定交换设备的所有位置信息
 * @param[in] exchange_dev_id 交换设备ID
 */
void compete_manage::compete_manage_remove_device_info(int exchange_dev_id)
{
    std::lock_guard<std::mutex> lock(m_x_track_lock_mutex);
    
    auto it = m_x_track_lock_map.find(exchange_dev_id);
    if (it != m_x_track_lock_map.end())
    {
        m_x_track_lock_map.erase(it);
        SPDLOG_INFO("X-track: Removed all info for device {}", exchange_dev_id);
    }
    else
    {
        SPDLOG_WARN("X-track: Device {} not found in map", exchange_dev_id);
    }
    
    // 同时删除设备当前位置信息
    auto pos_it = m_exchange_dev_current_pos_map.find(exchange_dev_id);
    if (pos_it != m_exchange_dev_current_pos_map.end())
    {
        m_exchange_dev_current_pos_map.erase(pos_it);
        SPDLOG_INFO("X-track: Removed current position info for device {}", exchange_dev_id);
    }
}

/**
 * @brief X轨道路径申请函数
 * @details 为交换设备申请X轨道上的可用路径长度
 * @param[in] exchange_dev_id 交换设备ID
 * @param[in] curr_x_pos 当前X轨道位置
 * @return 返回可用的路径长度，-1表示申请失败
 */
int compete_manage::compete_manage_x_track_route_apply(int exchange_dev_id, int curr_x_pos, int direction)
{
    int valid_temp_old = -1;
    int valid_temp_curr = 0;
    // 注意：此函数现在是私有函数，调用者负责加锁，这里不再加锁以避免死锁

    SPDLOG_INFO("X-track route apply: device {} at pos {}, direction {}",
               exchange_dev_id, curr_x_pos, direction);

    for (auto it = m_x_track_lock_map.begin(); it != m_x_track_lock_map.end(); it++)
    {
        SPDLOG_INFO("X-track lock info: device {} lock range [{}, {}]",
                   it->first, it->second.lock_start_pos, it->second.lock_end_pos);
    }

    for (auto it = m_x_track_lock_map.begin(); it != m_x_track_lock_map.end(); it++)
    {
        SPDLOG_INFO("X-track route apply: checking device {} with lock range [{}, {}]",
                   it->first, it->second.lock_start_pos, it->second.lock_end_pos);
        
        if (exchange_dev_id != it->first)
        {
            if ((curr_x_pos >= it->second.lock_start_pos) && (curr_x_pos <= it->second.lock_end_pos))
            {
                SPDLOG_INFO("X-track route apply: position conflict with device {}", it->first);
                return -1;
            }

            if (direction > 0) // 正向运动（位置增大）
            {
                if (curr_x_pos < it->second.lock_start_pos)
                {
                    valid_temp_curr = it->second.lock_start_pos - curr_x_pos - m_slot_width * (m_safety_distance_slot_count + 1);
                    if (valid_temp_curr > 0)
                    {
                        SPDLOG_INFO("X-track route apply: forward valid length to device {} is {}",
                                   it->first, valid_temp_curr);
                        
                        if (valid_temp_old == -1 || valid_temp_curr < valid_temp_old)
                        {
                            valid_temp_old = valid_temp_curr;
                        }
                    }
                }
                else if (curr_x_pos > it->second.lock_end_pos)
                {
                    valid_temp_old = 0;
                    SPDLOG_INFO("X-track route apply: device {} is behind, no impact on forward motion", it->first);
                }
            }
            else if (direction < 0) // 反向运动（位置减小）
            {
                if (curr_x_pos > it->second.lock_end_pos)
                {
                    valid_temp_curr = curr_x_pos - it->second.lock_end_pos - m_slot_width * (m_safety_distance_slot_count + 1);
                    if (valid_temp_curr > 0)
                    {
                        SPDLOG_INFO("X-track route apply: backward valid length to device {} is {}",
                                   it->first, valid_temp_curr);
                        
                        if (valid_temp_old == -1 || valid_temp_curr < valid_temp_old)
                        {
                            valid_temp_old = valid_temp_curr;
                        }
                    }
                }
                else if (curr_x_pos < it->second.lock_start_pos)
                {
                    valid_temp_old = 0;
                    SPDLOG_INFO("X-track route apply: device {} is ahead, no impact on backward motion", it->first);
                }
            }

            SPDLOG_INFO("X-track route apply: valid_temp_old={}, valid_temp_curr={}",
                       valid_temp_old, valid_temp_curr);
        }
        else
        {
            SPDLOG_INFO("X-track route apply: found self device {}", exchange_dev_id);
        }
    }

    if (valid_temp_old == -1)
    {
        if (direction > 0) // 正向运动
        {
            valid_temp_curr = m_x_track_total_length - curr_x_pos - m_slot_width * (m_safety_distance_slot_count + 1);
        }
        else if (direction < 0) // 反向运动
        {
            valid_temp_curr = curr_x_pos;
        }
        else // direction == 0，默认使用轨道总长度
        {
            valid_temp_curr = m_x_track_total_length;
        }
        SPDLOG_INFO("X-track route apply: no obstacles, use available track length {}", valid_temp_curr);
    }
    else if(valid_temp_old < -1)
    {
        valid_temp_curr = 0;
    }
    else
    {
        valid_temp_curr = valid_temp_old;
    }

    return valid_temp_curr;
}

/**
 * @brief 获取设备可以到达的最近位置，自动判断移动方向并自动锁定位置
 * @details 实现逻辑：
 *          1. 从保存的信息中获取当前位置，如果设备未锁定则自动锁定目标位置
 *          2. 根据目标位置判断运动方向（正向/反向/原地不动）
 *          3. 逐步搜索可达位置，检查与其他设备的碰撞
 *          4. 如果目标位置不可达，自动锁定最近可达位置（使用一个格口的安全距离）
 * @param exchange_dev_id 设备ID
 * @param target_x_pos 目标X位置
 * @param nearest_pos 返回的最近可达位置（通过指针）
 * @param is_compete_slot 是否为去竞争格口（当前实现中，不可达时统一使用一个格口的安全距离）
 * @return 返回是否可以到达目标位置，true表示可以到达目标位置，false表示只能到达中间位置
 */
bool compete_manage::compete_manage_x_track_get_nearest_position(int exchange_dev_id, int target_x_pos, int* nearest_pos, bool is_compete_slot)
{
    std::lock_guard<std::mutex> lock(m_x_track_lock_mutex);
    
    auto it = m_x_track_lock_map.find(exchange_dev_id);
    if (it == m_x_track_lock_map.end())
    {
        SPDLOG_INFO("Device {} not found in lock map, auto-locking at target position {}", exchange_dev_id, target_x_pos);
        
        // compete_manage_remove_device_info(exchange_dev_id);
        
        if (nearest_pos != nullptr)
        {
            *nearest_pos = target_x_pos;
        }
        
        SPDLOG_INFO("Device {} auto-locked successfully at position {}", exchange_dev_id, target_x_pos);
        return true;
    }
    
    // 获取设备当前位置
    auto pos_it = m_exchange_dev_current_pos_map.find(exchange_dev_id);
    int curr_x_pos = pos_it->second;
    
    int direction = 0;
    if (target_x_pos > curr_x_pos)
    {
        direction = 1;  // 正向运动
    }
    else
    {
        direction = -1; // 反向运动
    }
    
    int available_length = compete_manage_x_track_route_apply(exchange_dev_id, curr_x_pos, direction);
    
    if (available_length == -1)
    {
        SPDLOG_ERROR("Position conflict for device {}", exchange_dev_id);
        *nearest_pos = 0;
        return false;
    }
    
    int distance_to_target = abs(target_x_pos - curr_x_pos);
    bool can_reach_target = false;
    
    if (available_length >= distance_to_target)
    {
        *nearest_pos = target_x_pos;
        can_reach_target = true;

        compete_manage_x_track_lock_by_direction(exchange_dev_id, target_x_pos, direction, is_compete_slot);
        SPDLOG_INFO("Device {} automatically locked at target position: {}",
                   exchange_dev_id, target_x_pos);
    }
    else
    {
        if (direction > 0)
        {
            *nearest_pos = curr_x_pos + available_length;
        }
        else
        {
            *nearest_pos = curr_x_pos - available_length;
        }
        can_reach_target = false;
        
        compete_manage_x_track_lock_by_direction(exchange_dev_id, *nearest_pos, direction, false);
        SPDLOG_INFO("Device {} automatically locked at nearest position: {} - single slot for unreachable target)",
                   exchange_dev_id, *nearest_pos);
    }
    
    return can_reach_target;
}

/**
 * @brief 更新交换设备当前位置
 * @details 更新交换设备在X轨道上的当前位置
 * @param[in] exchange_dev_id 交换设备ID
 * @param[in] current_pos 当前X轨道位置
 */
void compete_manage::compete_manage_update_current_position(int exchange_dev_id, int current_pos)
{
    std::lock_guard<std::mutex> lock(m_x_track_lock_mutex);
    
    // 检查是否已经有当前交换设备信息
    auto it = m_exchange_dev_current_pos_map.find(exchange_dev_id);
    if (it != m_exchange_dev_current_pos_map.end())
    {
        // 设备信息存在，获取原来的值并判断运动方向
        int old_pos = it->second;
        int direction = 0;
        std::string direction_str = "stationary";
        
        if (current_pos > old_pos)
        {
            direction = 1;
            direction_str = "forward";
        }
        else if (current_pos < old_pos)
        {
            direction = -1;
            direction_str = "backward";
        }
        
        // 更新位置
        it->second = current_pos;
        SPDLOG_INFO("Updated exchange device {} position from {} to {} (direction: {}, value: {})",
                   exchange_dev_id, old_pos, current_pos, direction_str, direction);
        
        // 根据移动方向释放锁定位置
        compete_manage_x_track_release_by_direction(exchange_dev_id, current_pos, direction);
    }
    else
    {
        // 设备信息不存在，插入新信息
        m_exchange_dev_current_pos_map.insert(std::make_pair(exchange_dev_id,  current_pos));
        compete_manage_x_track_release(exchange_dev_id, current_pos);
        SPDLOG_INFO("Inserted new exchange device {} with current position: {}", exchange_dev_id, current_pos);
    }
}

/**
 * @brief 获取交换设备当前位置
 * @details 获取交换设备在X轨道上的当前位置
 * @param[in] exchange_dev_id 交换设备ID
 * @param[out] current_pos 当前X轨道位置
 * @return 成功返回true，失败返回false
 */
bool compete_manage::compete_manage_get_current_position(int exchange_dev_id, int* current_pos)
{
    if (current_pos == nullptr)
    {
        SPDLOG_ERROR("Invalid parameter: current_pos is null");
        return false;
    }

    std::lock_guard<std::mutex> lock(m_x_track_lock_mutex);
    auto it = m_exchange_dev_current_pos_map.find(exchange_dev_id);
    if (it != m_exchange_dev_current_pos_map.end())
    {
        *current_pos = it->second;
        SPDLOG_INFO("Retrieved exchange device {} current position: {}", exchange_dev_id, *current_pos);
        return true;
    }
    else
    {
        SPDLOG_WARN("Exchange device {} current position not found", exchange_dev_id);
        return false;
    }
}
/**
 * @brief 根据移动方向释放X轨道锁定位置
 * @details 根据设备的移动方向智能释放锁定位置，避免不必要的锁定
 * @param[in] exchange_dev_id 交换设备ID
 * @param[in] direction 移动方向，1表示正向（增大方向），-1表示反向（减小方向），0表示静止
 */
void compete_manage::compete_manage_x_track_release_by_direction(int exchange_dev_id, int current_pos, int direction)
{   
    // std::lock_guard<std::mutex> lock(m_x_track_lock_mutex);
    
    // 根据exchange_dev_id查找设备锁定信息
    auto it = m_x_track_lock_map.find(exchange_dev_id);
    if (it == m_x_track_lock_map.end())
    {
        SPDLOG_WARN("Exchange device {} not found in lock map", exchange_dev_id);
        return;
    }
    
    // 根据方向决定释放策略
    if (direction == 0)
    {
        // 静止状态，保持当前锁定
        SPDLOG_INFO("Device {} is stationary, keeping current lock", exchange_dev_id);
    }
    else if (direction > 0)
    {
        // 正向移动，修改start位置锁定（释放后方锁定）
        int old_start = it->second.lock_start_pos;
        it->second.lock_start_pos = current_pos - m_slot_width * (m_safety_distance_slot_count + 1);
        SPDLOG_INFO("Forward movement: updated device {} lock start from {} to {} (released rear lock)",
                   exchange_dev_id, old_start, it->second.lock_start_pos);
    }
    else if (direction < 0)
    {
        // 反向移动，修改end位置锁定（释放前方锁定）
        int old_end = it->second.lock_end_pos;
        it->second.lock_end_pos = current_pos + m_slot_width  * (m_safety_distance_slot_count + 1);
        SPDLOG_INFO("Backward movement: updated device {} lock end from {} to {} (released front lock)",
                   exchange_dev_id, old_end, it->second.lock_end_pos);
    }
}

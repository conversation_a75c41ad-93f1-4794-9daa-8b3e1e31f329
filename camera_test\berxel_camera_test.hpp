/**
 * @file berxel_camera_test.hpp
 * @brief Berxel相机测试程序头文件
 * @details 提取berxel相机核心功能，用于灰度算法测试
 * <AUTHOR> from original camera_drive_berxel
 * @date 2025-08-26
 * @version v1.0.0
 */

#ifndef __BERXEL_CAMERA_TEST_HPP__
#define __BERXEL_CAMERA_TEST_HPP__

#include <string>
#include <iostream>
#include <vector>
#include <memory>
#include <mutex>
#include <thread>
#include <functional>
#include <fstream>
#include <cmath>
#include <queue>
#include <condition_variable>
#include <atomic>
#include <map>

// Berxel SDK头文件
#include "BerxelHawkContext.h"
#include "BerxelHawkDevice.h"
#include "BerxelHawkFrame.h"
#include "BerxelHawkDefines.h"

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <errno.h>
#include <spdlog/spdlog.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/sinks/stdout_color_sinks.h>

using namespace std;



// RGB888结构体
typedef struct {
    uint8_t r;
    uint8_t g;
    uint8_t b;
} RGB888;

// HSV结构体
typedef struct {
    float h;  // 色相 (0-360度)
    float s;  // 饱和度 (0-1)
    float v;  // 明度 (0-1)
} HSV;

// BMP文件头结构体
#pragma pack(push, 1)
typedef struct {
    uint16_t bfType;        // 文件类型，必须是0x4D42 ('BM')
    uint32_t bfSize;        // 文件大小
    uint16_t bfReserved1;   // 保留字段
    uint16_t bfReserved2;   // 保留字段
    uint32_t bfOffBits;     // 数据偏移量
} BMPFILEHEADER;

// BMP信息头结构体
typedef struct {
    uint32_t biSize;          // 信息头大小
    uint32_t biWidth;         // 图像宽度
    uint32_t biHeight;        // 图像高度
    uint16_t biPlanes;        // 颜色平面数
    uint16_t biBitCount;      // 每像素位数
    uint32_t biCompression;   // 压缩类型
    uint32_t biSizeImage;     // 图像数据大小
    uint32_t biXPelsPerMeter; // 水平分辨率
    uint32_t biYPelsPerMeter; // 垂直分辨率
    uint32_t biClrUsed;       // 使用的颜色数
    uint32_t biClrImportant;  // 重要颜色数
} BMPINFOHEADER;
#pragma pack(pop)

// 2D点位置结构体
typedef struct {
    int x;
    int y;
} point_pos_2d;

// 相机工作模式枚举
typedef enum {
    DEV_WORK_MODE_NULL = 0,
    DEV_WORK_MODE_COLOR = 1,
    DEV_WORK_MODE_GRAY = 2,
    DEV_WORK_MODE_DEPTH = 3,
    DEV_WORK_MODE_COLOR_DEPTH = 4,
} camera_work_mode;

// 相机自动曝光参数
typedef struct {
    bool auto_ae;
    uint32_t exposure_time;
    uint32_t gain;
} camera_ae_para;

// 相机配置参数
typedef struct {
    bool valid_flag;
    std::string addr;
    point_pos_2d data_valid[4];  // 有效区域四个顶点
    uint32_t frame_rate;
    uint8_t gray_base;
    uint8_t gray_err;
    float depth_base;
    float depth_err;
    uint32_t id;
    uint32_t area_mask;
    camera_work_mode mode;
    camera_ae_para ae_para;
} camera_config;

// 灰度物品检测结果
typedef struct {
    bool object_detected;        // 是否检测到物品
    uint32_t detected_area;      // 检测到的物品面积（像素数）
    uint8_t background_gray;     // 背景灰度值
    uint8_t detection_threshold; // 检测阈值
    uint32_t frame_index;        // 帧索引
} gray_detection_result;

// 深度物品检测结果
typedef struct {
    bool object_detected;        // 是否检测到物品
    uint32_t detected_area;      // 检测到的物品面积（像素数）
    uint16_t background_depth;   // 背景深度值
    uint16_t detection_threshold; // 检测阈值
    uint32_t frame_index;        // 帧索引
    uint16_t min_object_depth;   // 检测到物体的最小深度
    uint16_t max_object_depth;   // 检测到物体的最大深度
    uint16_t avg_object_depth;   // 检测到物体的平均深度
    uint32_t total_valid_pixels; // 有效像素总数
} depth_detection_result;

// RGB彩色物品检测结果
typedef struct {
    bool object_detected;        // 是否检测到物品
    uint32_t detected_area;      // 检测到的物品面积（像素数）
    RGB888 background_color;     // 背景颜色值
    float color_threshold;       // 颜色差异阈值
    uint32_t frame_index;        // 帧索引
    RGB888 avg_object_color;     // 检测到物体的平均颜色
    float min_color_diff;        // 最小颜色差异
    float max_color_diff;        // 最大颜色差异
    float avg_color_diff;        // 平均颜色差异
    uint32_t total_valid_pixels; // 有效像素总数
    float detection_threshold;   // 检测阈值
    float dominant_hue;          // 主导色相
    double color_variance;       // 颜色方差
} rgb_detection_result;

// 帧数据回调函数类型
typedef std::function<void(berxel::BerxelHawkStreamType, berxel::BerxelHawkFrame*, void*)> FrameCallback;

/**
 * @brief Berxel相机测试类
 */
class BerxelCameraTest {
// 帧数据结构体
    struct FrameData {
        uint32_t frame_index;
        std::vector<RGB888> color_data;
        std::vector<uint16_t> depth_data;
        int width;
        int height;
        std::chrono::steady_clock::time_point timestamp;
        
        FrameData() : frame_index(0), width(0), height(0) {}
        FrameData(uint32_t idx, const std::vector<RGB888>& color, const std::vector<uint16_t>& depth,
                  int w, int h) : frame_index(idx), color_data(color), depth_data(depth),
                  width(w), height(h), timestamp(std::chrono::steady_clock::now()) {}
    };
public:
    BerxelCameraTest();
    ~BerxelCameraTest();

    // 初始化相机
    bool init();
    
    // 启动相机流
    bool start();
    
    // 停止相机流
    void stop();
    
    // 设置帧数据回调函数
    void set_frame_callback(FrameCallback callback);
    
    // 获取灰度图像数据
    bool get_gray_image(uint8_t* gray_data, int& width, int& height);
    
    // 获取彩色图像数据
    bool get_color_image(RGB888* color_data, int& width, int& height);
    
    // 获取深度图像数据
    bool get_depth_image(uint16_t* depth_data, int& width, int& height);
    
    // RGB转灰度
    static uint8_t rgb_to_gray(const RGB888& rgb);
    
    // 保存图像数据到BMP文件
    bool save_gray_image_bmp(const std::string& filename, const uint8_t* data, int width, int height);
    bool save_color_image_bmp(const std::string& filename, const RGB888* data, int width, int height);
    bool save_depth_image_bmp(const std::string& filename, const uint16_t* data, int width, int height);
    
    // 获取相机信息
    bool get_camera_info(std::string& info);
    
    // 基于平面拟合的深度图像修复
    static void plane_fitting_repair(const uint16_t* input_data, uint16_t* output_data, 
                                  int width, int height, int window_size = 5, double max_distance = 50.0);
    
    // 深度图像直方图均衡化处理
    static void histogram_equalization_depth(const uint16_t* input_data, uint16_t* output_data, 
                                          int width, int height, uint16_t min_depth = 0, uint16_t max_depth = 65535);
    
    // 保存直方图均衡化后的深度图像
    bool save_hist_equalized_depth_image_bmp(const std::string& filename, const uint16_t* depth_data,
                                       int width, int height);
    // 彩色图像高斯模糊处理
    static void gaussian_blur_color(const RGB888* input_data, RGB888* output_data,
                                   int width, int height, double sigma = 1.0, int kernel_size = 5);
    
    // 保存高斯模糊后的彩色图像
    bool save_gaussian_blurred_color_image_bmp(const std::string& filename, const RGB888* color_data,
                                             int width, int height, double sigma = 1.0, int kernel_size = 5);
                                             
    // RGB转HSV
    static HSV rgb_to_hsv(const RGB888& rgb);
    
    // 将RGB图像转换为HSV图像
    static void convert_rgb_to_hsv(const RGB888* input_data, HSV* output_data, int width, int height);
    
    // 保存HSV图像为BMP文件
    bool save_hsv_image_bmp(const std::string& filename, const HSV* hsv_data, int width, int height);
    
    // 灰度物品检测相关方法
    // 配置灰度物品检测参数
    void configure_gray_detection(const point_pos_2d valid_coords[4], uint8_t gray_mask, uint32_t area_mask);
    
    // // 执行灰度物品检测
    // gray_detection_result detect_gray_object(berxel::BerxelHawkFrame* pFrame);
    
    // 判断点是否在有效检测区域内
    bool is_position_valid_for_detection(int x, int y, const point_pos_2d* valid_coords);
    
    // 计算动态灰度阈值
    uint8_t calculate_dynamic_gray_threshold(berxel::BerxelHawkFrame* pFrame, const point_pos_2d* calib_points);
    
    // 计算动态灰度阈值（从帧数据）
    uint8_t calculate_dynamic_gray_threshold(const FrameData& frame_data, const point_pos_2d* calib_points);

    // 深度物品检测相关方法
    // 配置深度物品检测参数
    void configure_depth_detection(const point_pos_2d valid_coords[4], uint16_t depth_threshold, uint32_t area_mask);
    
    // // 执行深度物品检测
    // depth_detection_result detect_depth_object(berxel::BerxelHawkFrame* pFrame);
    
    // 计算动态深度阈值
    uint16_t calculate_dynamic_depth_threshold(const FrameData& frame_data, const point_pos_2d* calib_points);
    
    // 从帧数据计算背景深度的辅助函数
    uint16_t calculate_background_depth_from_data(const FrameData& frame_data);
    
    // 在有效区域内检测物品的辅助函数
    void detect_objects_in_valid_area(const FrameData& frame_data, depth_detection_result& result);

    

private:
    // 静态回调函数
    static void frame_callback(berxel::BerxelHawkStreamType streamType, 
                             berxel::BerxelHawkFrame* pFrame, 
                             void* pUserData);
    
    // 加载配置参数
    bool load_config();
    
    // 图像处理相关私有方法
    void process_frame_data(const FrameData& frame_data);
    gray_detection_result detect_gray_object(const FrameData& frame_data);
    depth_detection_result detect_depth_object_from_data(const FrameData& frame_data);

    // RGB彩色物品检测
    rgb_detection_result detect_rgb_object_from_data(const RGB888* color_data, int width, int height);
    void detect_rgb_object_from_frame_data(const FrameData& frame_data);
    
    // RGB检测辅助函数
    RGB888 calculate_dynamic_rgb_threshold(const RGB888* color_data, int width, int height);
    void set_rgb_detection_params(const point_pos_2d coords[4], uint8_t color_threshold, int area_mask);
    double calculate_rgb_color_difference(const RGB888& color1, const RGB888& color2);

private:
    berxel::BerxelHawkContext* m_context;
    berxel::BerxelHawkDevice* m_device;
    berxel::BerxelHawkDeviceInfo m_device_info;
    
    camera_config m_config;
    
    FrameCallback m_frame_callback;
    
    std::mutex m_frame_mutex;
    std::vector<uint8_t> m_latest_gray_frame;
    std::vector<RGB888> m_latest_color_frame;
    std::vector<uint16_t> m_latest_depth_frame;
    
    int m_frame_width;
    int m_frame_height;
    
    bool m_is_running;
    uint32_t m_frame_count;
    
    std::string m_log_path;
    
    // 灰度物品检测相关成员变量
    point_pos_2d m_detection_coords[4];  // 检测区域四个顶点
    uint8_t m_gray_mask;                 // 灰度阈值偏移
    uint32_t m_area_mask;                // 最小检测面积
    gray_detection_result m_latest_result; // 最新检测结果
    mutable std::mutex m_detection_mutex;  // 检测结果互斥锁（mutable允许在const函数中使用）
    // 深度物品检测相关成员变量
    point_pos_2d m_depth_detection_coords[4];  // 深度检测区域四个顶点
    uint16_t m_depth_threshold;               // 深度阈值
    uint32_t m_depth_area_mask;               // 深度检测最小面积
    depth_detection_result m_latest_depth_result; // 最新深度检测结果
    mutable std::mutex m_depth_detection_mutex;   // 深度检测结果互斥锁

    // RGB检测相关成员变量
    float m_rgb_color_threshold;                  // RGB颜色差异阈值
    uint32_t m_rgb_area_mask;                     // RGB检测最小面积
    rgb_detection_result m_latest_rgb_result;     // 最新RGB检测结果
    mutable std::mutex m_rgb_detection_mutex;     // RGB检测结果互斥锁
    int m_rgb_detection_count;                    // RGB检测总次数
    int m_rgb_object_detected_count;              // RGB物品检测成功次数
    std::mutex m_rgb_stats_mutex;                 // RGB统计数据互斥锁
    
    std::queue<FrameData> m_frame_queue;           // 帧数据队列
    std::mutex m_frame_queue_mutex;                // 帧队列互斥锁
    std::condition_variable m_frame_condition;     // 帧队列条件变量
    std::thread m_processing_thread;               // 图像处理线程
    std::atomic<bool> m_processing_thread_running; // 图像处理线程运行标志
    static const size_t MAX_QUEUE_SIZE = 10;       // 最大队列大小
    size_t m_max_queue_size;                       // 实际使用的最大队列大小
    
    // 深度检测统计计数器
    int m_depth_detection_count;                   // 深度检测总次数
    int m_depth_object_detected_count;             // 深度物品检测成功次数
    std::mutex m_depth_stats_mutex;                // 深度统计数据互斥锁
    
    // 灰度检测统计计数器
    int m_gray_detection_count;                    // 灰度检测总次数
    int m_gray_object_detected_count;              // 灰度物品检测成功次数
    std::mutex m_gray_stats_mutex;                 // 灰度统计数据互斥锁
    
    // 图像处理线程函数
    void image_processing_thread();
    
    // 启动图像处理线程
    void start_processing_thread();
    
    // 停止图像处理线程
    void stop_processing_thread();
    
    // 添加帧数据到队列（自动生成时间戳）
    void add_frame_to_queue(uint32_t frame_index, const std::vector<RGB888>& color_data,
                           const std::vector<uint16_t>& depth_data, int width, int height);
    
    // 添加帧数据到队列（指定时间戳）
    void add_frame_to_queue(uint32_t frame_index, const std::vector<RGB888>& color_data,
                           const std::vector<uint16_t>& depth_data, int width, int height,
                           std::chrono::steady_clock::time_point timestamp);
    
    // 帧处理辅助函数
    void print_frame_info(const FrameData& frame_data);                    // 打印帧信息
    
    // 创建标记了检测像素点的深度图像
    std::vector<uint16_t> create_marked_depth_image(const FrameData& frame_data, const depth_detection_result& result);
    std::vector<RGB888> create_marked_color_image(const FrameData& frame_data, const rgb_detection_result& result);
};

#endif // __BERXEL_CAMERA_TEST_HPP__
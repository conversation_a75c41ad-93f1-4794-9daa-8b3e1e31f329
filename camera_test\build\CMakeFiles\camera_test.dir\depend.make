# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/BerxelHawkContext.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/BerxelHawkDefines.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/BerxelHawkDevice.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/BerxelHawkFrame.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/BerxelHawkPlatform.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/common-inl.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/common.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/backtracer-inl.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/backtracer.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/circular_q.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/console_globals.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/file_helper-inl.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/file_helper.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/fmt_helper.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/log_msg-inl.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/log_msg.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/log_msg_buffer-inl.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/log_msg_buffer.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/null_mutex.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/os-inl.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/os.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/periodic_worker-inl.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/periodic_worker.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/registry-inl.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/registry.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/synchronous_factory.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/details/windows_include.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/fmt/bundled/core.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/fmt/bundled/format-inl.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/fmt/bundled/format.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/fmt/fmt.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/formatter.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/logger-inl.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/logger.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/pattern_formatter-inl.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/pattern_formatter.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink-inl.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/sinks/base_sink-inl.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/sinks/base_sink.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/sinks/rotating_file_sink-inl.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/sinks/rotating_file_sink.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/sinks/sink-inl.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/sinks/sink.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/sinks/stdout_color_sinks-inl.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/sinks/stdout_color_sinks.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/sinks/wincolor_sink-inl.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/sinks/wincolor_sink.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/spdlog-inl.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/spdlog.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/tweakme.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../../share/libs/x86/include/spdlog/version.h
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../berxel_camera_test.cpp
CMakeFiles/camera_test.dir/berxel_camera_test.cpp.o: ../berxel_camera_test.hpp

CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/BerxelHawkContext.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/BerxelHawkDefines.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/BerxelHawkDevice.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/BerxelHawkFrame.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/BerxelHawkPlatform.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/common-inl.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/common.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/backtracer-inl.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/backtracer.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/circular_q.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/console_globals.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/file_helper-inl.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/file_helper.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/fmt_helper.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/log_msg-inl.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/log_msg.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/log_msg_buffer-inl.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/log_msg_buffer.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/null_mutex.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/os-inl.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/os.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/periodic_worker-inl.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/periodic_worker.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/registry-inl.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/registry.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/synchronous_factory.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/details/windows_include.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/fmt/bundled/core.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/fmt/bundled/format-inl.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/fmt/bundled/format.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/fmt/fmt.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/formatter.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/logger-inl.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/logger.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/pattern_formatter-inl.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/pattern_formatter.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink-inl.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/sinks/ansicolor_sink.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/sinks/base_sink-inl.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/sinks/base_sink.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/sinks/rotating_file_sink-inl.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/sinks/rotating_file_sink.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/sinks/sink-inl.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/sinks/sink.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/sinks/stdout_color_sinks-inl.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/sinks/stdout_color_sinks.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/sinks/wincolor_sink-inl.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/sinks/wincolor_sink.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/spdlog-inl.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/spdlog.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/tweakme.h
CMakeFiles/camera_test.dir/main.cpp.o: ../../share/libs/x86/include/spdlog/version.h
CMakeFiles/camera_test.dir/main.cpp.o: ../berxel_camera_test.hpp
CMakeFiles/camera_test.dir/main.cpp.o: ../main.cpp


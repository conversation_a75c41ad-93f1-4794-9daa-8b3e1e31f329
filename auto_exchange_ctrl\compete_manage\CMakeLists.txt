# CMakeLists.txt for compete_manage module
# 取换箱设备X轴防碰撞管理器构建配置

cmake_minimum_required(VERSION 3.5)

# 设置项目名称
project(compete_manage)

# 设置C++标准
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O2 -pthread")

# 设置头文件搜索路径
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    # 添加项目根目录和相关依赖路径
    "../"
    "../../"
    # 添加spdlog头文件路径（参考顶层CMakeLists.txt配置）
    "../../share/libs/x86/include"
)

# 设置库文件搜索路径
link_directories(
    "../../share/libs/x86/lib"
)

# 设置头文件
set(COMPETE_MANAGE_HEADERS
    compete_manage.hpp
)

# 设置源文件
set(COMPETE_MANAGE_SOURCES
    compete_manage.cpp
)

# 安装头文件
install(FILES ${COMPETE_MANAGE_HEADERS}
    DESTINATION include/compete_manage
)

# 创建示例程序（默认启用）
option(BUILD_COMPETE_MANAGE_EXAMPLE "Build compete_manage example" ON)

if(BUILD_COMPETE_MANAGE_EXAMPLE)
    add_executable(compete_manage_example 
        example.cpp 
        ${COMPETE_MANAGE_SOURCES}
    )
    # 示例程序需要链接基本库和spdlog
    target_link_libraries(compete_manage_example 
        pthread
        spdlog
    )
endif()

# 输出配置信息
message(STATUS "compete_manage configuration:")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Install Prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "  Note: compete_manage includes both header and implementation files")
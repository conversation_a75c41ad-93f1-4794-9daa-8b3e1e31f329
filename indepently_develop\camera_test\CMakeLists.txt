cmake_minimum_required(VERSION 3.5)

SET( CROSS_COMPILE OFF )

SET(CMAKE_SYSTEM_NAME Linux)

if(CROSS_COMPILE)
	SET(CMAKE_C_COMPILER "/usr/bin/arm-linux-gnueabihf-gcc")
	SET(CMAKE_CXX_COMPILER "/usr/bin/arm-linux-gnueabihf-g++")
	link_directories("../share/libs/arm/lib")
	include_directories("../share/libs/arm/include")
else()
	SET(CMAKE_C_COMPILER "/usr/bin/gcc")
	SET(CMAKE_CXX_COMPILER "/usr/bin/g++")
	link_directories("../share/libs/x86/lib")
	include_directories("../share/libs/x86/include")
	SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -m64")
endif()

add_definitions(-Wall)

project(camera_test LANGUAGES CXX C)

set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -pthread -g")

# 包含目录
include_directories("../")
include_directories("./")
include_directories("../share/libs/include")

# 链接目录
link_directories("../share/libs/lib")

# 链接库
link_libraries(spdlog BerxelHawk)

# 默认当前文件夹下所有文件均参与编译
aux_source_directory(. DIR_SRCS)

# 生成可执行文件
add_executable(camera_test ${DIR_SRCS})

# 添加外部库依赖
target_link_libraries(camera_test)
#IncludeRegexLine: ^[ 	]*#[ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../../share/libs/x86/include/BerxelHawkContext.h
BerxelHawkPlatform.h
-
BerxelHawkDefines.h
-

../../share/libs/x86/include/BerxelHawkDefines.h
stdint.h
-

../../share/libs/x86/include/BerxelHawkDevice.h
map
-
vector
-
BerxelHawkPlatform.h
-
BerxelHawkDefines.h
-
BerxelHawkFrame.h
-

../../share/libs/x86/include/BerxelHawkFrame.h
BerxelHawkPlatform.h
-
BerxelHawkDefines.h
-

../../share/libs/x86/include/BerxelHawkPlatform.h
stddef.h
-

/media/sf_work/auto_sort_high_efficient/camera_test/berxel_camera_test.cpp
berxel_camera_test.hpp
/media/sf_work/auto_sort_high_efficient/camera_test/berxel_camera_test.hpp
iostream
-
chrono
-
thread
-
iomanip
-
algorithm
-
cmath
-

/media/sf_work/auto_sort_high_efficient/camera_test/berxel_camera_test.hpp
string
-
iostream
-
vector
-
memory
-
mutex
-
thread
-
functional
-
fstream
-
cmath
-
queue
-
condition_variable
-
atomic
-
map
-
BerxelHawkContext.h
/media/sf_work/auto_sort_high_efficient/camera_test/BerxelHawkContext.h
BerxelHawkDevice.h
/media/sf_work/auto_sort_high_efficient/camera_test/BerxelHawkDevice.h
BerxelHawkFrame.h
/media/sf_work/auto_sort_high_efficient/camera_test/BerxelHawkFrame.h
BerxelHawkDefines.h
/media/sf_work/auto_sort_high_efficient/camera_test/BerxelHawkDefines.h
stdio.h
-
stdlib.h
-
string.h
-
stdint.h
-
unistd.h
-
errno.h
-

/media/sf_work/auto_sort_high_efficient/camera_test/main.cpp
berxel_camera_test.hpp
/media/sf_work/auto_sort_high_efficient/camera_test/berxel_camera_test.hpp
iostream
-
chrono
-
thread
-
signal.h
-
cmath
-
iomanip
-


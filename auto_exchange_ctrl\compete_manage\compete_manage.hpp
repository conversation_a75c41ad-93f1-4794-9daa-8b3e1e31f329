/**@file  	   compete_manage.hpp
* @brief       取换箱设备X轴防碰撞管理器
* @details     实现取换箱设备在X轴上的防碰撞功能，包括位置检测、轨道锁定、最近可达位置计算等
* <AUTHOR>
* @date        2025-01-05
* @version     v1.0.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.1.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2025/01/05  <td>1.0.0    <td>MLC       <td>初始版本	                   </tr>
* </table>
*
**********************************************************************************
*/

#ifndef __COMPETE_MANAGE_HPP__
#define __COMPETE_MANAGE_HPP__

#include <list>
#include <unordered_map>
#include <mutex>

/**
 * @brief x轨道锁定结构体
 * @details 用于记录交换设备的轨道锁定信息
 */
typedef struct __x_track_lock_info
{
	int exchange_dev_id;    // 交换设备id
	int lock_start_pos;     // 锁定开始位置
	int lock_end_pos;       // 锁定结束位置
	int safety_distance;    // 安全距离
}x_track_lock_info;

/**
 * @brief x轨道锁定信息容器类型定义
 * @details 使用unordered_map存储交换设备ID到轨道锁定信息的映射关系
 */
typedef std::unordered_map<int, x_track_lock_info> x_track_lock_map;

/**
 * @brief 防碰撞管理器类（单例模式）
 */
class compete_manage
{
public:
    /// 获取单例实例
    static compete_manage& get_instance()
    {
        static compete_manage instance;
        return instance;
    }

    /// 初始化防碰撞管理器的相关参数和数据结构
    void compete_manage_init();

    /// 更新交换设备在X轨道上的当前位置
    void compete_manage_update_current_position(int exchange_dev_id, int current_pos);

    /// 获取交换设备在X轨道上的当前位置
    bool compete_manage_get_current_position(int exchange_dev_id, int* current_pos);

    /// 获取设备可以到达的最近位置，自动判断移动方向并自动锁定位置
    bool compete_manage_x_track_get_nearest_position(int exchange_dev_id, int target_x_pos, int* nearest_pos, bool is_compete_slot = false);

    // ========== 轨道锁定和释放函数 ==========
    /// 根据移动方向和竞争格口状态锁定X轨道目标位置，智能计算安全距离
    void compete_manage_x_track_lock_by_direction(int exchange_dev_id, int target_pos, int direction, bool is_compete_slot = false);

    /// 为交换设备在X轨道上解锁位置
    void compete_manage_x_track_release(int exchange_dev_id, int x_pos);

    /// 根据移动方向智能释放锁定位置，避免不必要的锁定
    void compete_manage_x_track_release_by_direction(int exchange_dev_id, int current_pos, int direction);

    /// 删除指定交换设备的所有位置信息
    void compete_manage_remove_device_info(int exchange_dev_id);
    
protected:
    /// 虚析构函数
    virtual ~compete_manage() = default;

private:
    /// 为交换设备申请X轨道上的可用路径长度，支持双向运动（内部函数，不加锁）
    int compete_manage_x_track_route_apply(int exchange_dev_id, int curr_x_pos, int direction);

    /// X轨道锁定操作的互斥锁，用于保护m_x_track_lock_map的线程安全访问
    std::mutex m_x_track_lock_mutex;

    /// x轨道锁定信息映射表，存储交换设备ID到轨道锁定信息的映射关系
    x_track_lock_map m_x_track_lock_map;

    /// 格口宽度，用于防碰撞计算
    int m_slot_width;

    /// 安全距离格口数量，定义防碰撞计算中的安全距离
    int m_safety_distance_slot_count;

    /// 交换设备当前位置映射表，存储交换设备ID到当前X轨道位置的映射关系
    std::unordered_map<int, int> m_exchange_dev_current_pos_map;

    /// X轨道总长度
    int m_x_track_total_length;
};

#endif // __COMPETE_MANAGE_HPP__
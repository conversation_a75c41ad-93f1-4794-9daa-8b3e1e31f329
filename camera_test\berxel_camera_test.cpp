/**
 * @file berxel_camera_test.cpp
 * @brief Berxel相机测试程序实现文件
 * @details 提取berxel相机核心功能，用于灰度算法测试
 * <AUTHOR> from original camera_drive_berxel
 * @date 2025-08-26
 * @version v1.0.0
 */

#include "berxel_camera_test.hpp"
#include <iostream>
#include <spdlog/spdlog.h>
#include <chrono>
#include <thread>
#include <iomanip>
#include <algorithm>
#include <cmath>
#include <arpa/inet.h>

BerxelCameraTest::BerxelCameraTest()
    : m_context(nullptr)
    , m_device(nullptr)
    , m_frame_width(640)
    , m_frame_height(400)
    , m_is_running(false)
    , m_frame_count(0)
    , m_log_path("./logs/camera_test")
    , m_gray_mask(15)
    , m_area_mask(500)
    , m_depth_threshold(50)
    , m_depth_area_mask(500)
    , m_max_queue_size(10)
    , m_depth_detection_count(0)
    , m_depth_object_detected_count(0)
    , m_processing_thread_running(false)
{
    // 初始化默认配置
    m_config.valid_flag = true;
    m_config.addr = "";
    m_config.frame_rate = 10;
    m_config.gray_base = 128;
    m_config.gray_err = 30;
    m_config.depth_base = 1000.0f;
    m_config.depth_err = 200.0f;
    m_config.id = 1;
    m_config.area_mask = 1000;
    m_config.mode = DEV_WORK_MODE_COLOR;
    
    // 默认有效区域（整个图像）
    m_config.data_valid[0] = {0, 0};
    m_config.data_valid[1] = {m_frame_width-1, 0};
    m_config.data_valid[2] = {0, m_frame_height-1};
    m_config.data_valid[3] = {m_frame_width-1, m_frame_height-1};
    
    // 有效区域通过 m_detection_coords 和 m_depth_detection_coords 表示
    
    // 初始化灰度检测有效区域已在后面单独设置
    
    // 默认曝光参数
    m_config.ae_para.auto_ae = true;
    m_config.ae_para.exposure_time = 3654;
    m_config.ae_para.gain = 3604;
    
    // 初始化灰度检测区域（默认为中心区域）
    m_detection_coords[0] = {100, 100};  // 左上角
    m_detection_coords[1] = {540, 100};  // 右上角
    m_detection_coords[2] = {100, 300};  // 左下角
    m_detection_coords[3] = {540, 300};  // 右下角
    
    // 初始化深度检测区域（默认与灰度检测区域相同）
    for (int i = 0; i < 4; i++) {
        m_depth_detection_coords[i] = m_detection_coords[i];
    }
    
    // 初始化灰度检测结果
    m_latest_result.object_detected = false;
    m_latest_result.detected_area = 0;
    m_latest_result.background_gray = 0;
    m_latest_result.detection_threshold = 0;
    m_latest_result.frame_index = 0;
    
    // 初始化深度检测结果
    m_latest_depth_result.object_detected = false;
    m_latest_depth_result.detected_area = 0;
    m_latest_depth_result.background_depth = 0;
    m_latest_depth_result.detection_threshold = 0;
    m_latest_depth_result.frame_index = 0;
    m_latest_depth_result.min_object_depth = 0;
    m_latest_depth_result.max_object_depth = 0;
    m_latest_depth_result.avg_object_depth = 0;
    
    // 初始化RGB检测参数
    m_rgb_color_threshold = 40.0f;  // RGB颜色差异阈值（非补光 口岸 35）
    m_rgb_area_mask = 10;          // RGB检测最小面积
    m_rgb_detection_count = 0;
    m_rgb_object_detected_count = 0;
    
    // 初始化灰度检测统计计数器
    m_gray_detection_count = 0;
    m_gray_object_detected_count = 0;
    
    // 初始化深度检测统计计数器
    m_depth_detection_count = 0;
    m_depth_object_detected_count = 0;
    
    // 初始化RGB检测结果
    m_latest_rgb_result.object_detected = false;
    m_latest_rgb_result.detected_area = 0;
    m_latest_rgb_result.background_color = {0, 0, 0};
    m_latest_rgb_result.color_threshold = 30.0f;
    m_latest_rgb_result.frame_index = 0;
    m_latest_rgb_result.avg_object_color = {0, 0, 0};
    m_latest_rgb_result.min_color_diff = 0.0f;
    m_latest_rgb_result.max_color_diff = 0.0f;
    m_latest_rgb_result.avg_color_diff = 0.0f;
    m_latest_rgb_result.total_valid_pixels = 0;
    m_latest_rgb_result.detection_threshold = 0.0f;
    m_latest_rgb_result.dominant_hue = 0.0f;
    m_latest_rgb_result.color_variance = 0.0;
    
    // 启动图像处理线程
    start_processing_thread();
}

BerxelCameraTest::~BerxelCameraTest() {
    // 停止图像处理线程
    stop_processing_thread();
    
    stop();
    
    // 给停止操作一些时间完成
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    if (m_device) {
        try {
            m_context->closeDevice(m_device);
        } catch (const std::exception& e) {
            std::cerr << "关闭设备时发生异常: " << e.what() << std::endl;
        }
        m_device = nullptr;
    }
    
    if (m_context) {
        try {
            berxel::BerxelHawkContext::destroyBerxelContext(m_context);
        } catch (const std::exception& e) {
            std::cerr << "销毁上下文时发生异常: " << e.what() << std::endl;
        }
        m_context = nullptr;
    }
    
    std::cout << "BerxelCameraTest 析构完成" << std::endl;
}

bool BerxelCameraTest::init() {
    std::cout << "初始化Berxel相机..." << std::endl;
    
    // 获取Berxel上下文
    m_context = berxel::BerxelHawkContext::getBerxelContext(m_log_path.c_str());
    if (!m_context) {
        std::cerr << "获取BerxelHawkContext失败" << std::endl;
        return false;
    }
    
    // 获取设备列表
    berxel::BerxelHawkDeviceInfo* pDeviceInfo = nullptr;
    uint32_t deviceCount = 0;
    m_context->getDeviceList(&pDeviceInfo, &deviceCount);
    
    std::cout << "发现设备数量: " << deviceCount << std::endl;
    
    if (deviceCount < 1 || !pDeviceInfo) {
        std::cerr << "未发现Berxel设备" << std::endl;
        return false;
    }
    
    // 使用第一个设备
    m_device_info = pDeviceInfo[0];
    std::cout << "设备信息: " << std::endl;
    std::cout << "  地址: " << m_device_info.deviceAddress << std::endl;
    std::cout << "  序列号: " << m_device_info.serialNumber << std::endl;
    std::cout << "  产品ID: " << m_device_info.productId << std::endl;
    
    // 打开设备
    m_device = m_context->openDevice(m_device_info);
    if (!m_device) {
        std::cerr << "打开Berxel设备失败: " << m_device_info.deviceAddress << std::endl;
        return false;
    }
    
    std::cout << "成功打开Berxel设备: " << m_device_info.deviceAddress << std::endl;
    
    // 设置系统时钟
    m_device->setSystemClock();
    
    // 配置流模式
    // m_device->setStreamFlagMode(berxel::BERXEL_HAWK_SINGULAR_STREAM_FLAG_MODE);
    m_device->setStreamFlagMode(berxel::BERXEL_HAWK_MIX_STREAM_FLAG_MODE);
    
    // 配置彩色流参数
    berxel::BerxelHawkStreamFrameMode colorFrameMode;
    int ret = m_device->getCurrentFrameMode(berxel::BERXEL_HAWK_COLOR_STREAM, &colorFrameMode);
    if (ret != 0) {
        std::cerr << "获取当前彩色流模式失败，错误码: " << ret << std::endl;
        return false;
    }
    std::cout << "当前彩色流模式: " << colorFrameMode.resolutionX << "x" << colorFrameMode.resolutionY 
              << " @" << colorFrameMode.framerate << "fps" << std::endl;
              
    colorFrameMode.resolutionX = m_frame_width;
    colorFrameMode.resolutionY = m_frame_height;
    colorFrameMode.framerate = m_config.frame_rate;
    ret = m_device->setFrameMode(berxel::BERXEL_HAWK_COLOR_STREAM, &colorFrameMode);
    if (ret != 0) {
        std::cerr << "设置彩色流模式失败，错误码: " << ret << std::endl;
        std::cerr << "尝试的分辨率: " << colorFrameMode.resolutionX << "x" << colorFrameMode.resolutionY << std::endl;
        std::cerr << "尝试的帧率: " << colorFrameMode.framerate << std::endl;
        return false;
    }
    std::cout << "彩色流模式设置成功: " << colorFrameMode.resolutionX << "x" << colorFrameMode.resolutionY 
              << " @" << colorFrameMode.framerate << "fps" << std::endl;
    
    // 配置深度流参数
    berxel::BerxelHawkStreamFrameMode depthFrameMode;
    ret = m_device->getCurrentFrameMode(berxel::BERXEL_HAWK_DEPTH_STREAM, &depthFrameMode);
    if (ret != 0) {
        std::cerr << "获取当前深度流模式失败，错误码: " << ret << std::endl;
        return false;
    }
    std::cout << "当前深度流模式: " << depthFrameMode.resolutionX << "x" << depthFrameMode.resolutionY
              << " @" << depthFrameMode.framerate << "fps" << std::endl;
              
    depthFrameMode.resolutionX = m_frame_width;
    depthFrameMode.resolutionY = m_frame_height;
    depthFrameMode.framerate = m_config.frame_rate;
    ret = m_device->setFrameMode(berxel::BERXEL_HAWK_DEPTH_STREAM, &depthFrameMode);
    if (ret != 0) {
        std::cerr << "设置深度流模式失败，错误码: " << ret << std::endl;
        std::cerr << "尝试的分辨率: " << depthFrameMode.resolutionX << "x" << depthFrameMode.resolutionY << std::endl;
        std::cerr << "尝试的帧率: " << depthFrameMode.framerate << std::endl;
        return false;
    }
    std::cout << "深度流模式设置成功: " << depthFrameMode.resolutionX << "x" << depthFrameMode.resolutionY
              << " @" << depthFrameMode.framerate << "fps" << std::endl;
    
// 设置网络参数 - IP地址为*************
    // berxel::BerxelHawkNetParams netParams;
    // memset(&netParams, 0, sizeof(netParams));
    
    // // 设置为静态IP模式
    // netParams.static_ip = 1;
    
    // // 设置IP地址为*************
    // // 将IP地址转换为网络字节序的uint32_t
    // uint32_t ip = (192 << 24) | (168 << 16) | (1 << 8) | 130;
    // netParams.ip_addr = htonl(ip);
    
    // // 设置子网掩码为*************
    // uint32_t netmask = (255 << 24) | (255 << 16) | (255 << 8) | 0;
    // netParams.net_mask = htonl(netmask);
    
    // // 设置网关为***********
    // uint32_t gateway = (192 << 24) | (168 << 16) | (1 << 8) | 131;
    // netParams.gw_addr = htonl(gateway);
    
    // // 设置DNS为***********
    // netParams.dns_addr = htonl(gateway);
    
    // // 禁用DHCP服务器
    // netParams.dhcps_enable = 0;
    
    // // 设置用户配置标志
    // netParams.user_setting = 1;
    
    // // 调用setNetParams设置网络参数
    // int ret_net = m_device->setNetParams(&netParams, sizeof(netParams));
    // if (ret_net != 0) {
    //     std::cerr << "设置网络参数失败，错误码: " << ret_net << std::endl;
    //     return false;
    // }
    // std::cout << "网络参数设置成功 - IP: *************" << std::endl;
    // int ret_slave = m_device->enableDeviceSlaveMode(true);
    // if (ret_slave != 0) {
    //     std::cerr << "设置设备从模式失败，错误码: " << ret_slave << std::endl;
    //     return false;
    // }
    // std::cout << "设备从模式设置成功" << std::endl;

    uint32_t curr_mode;
    m_device->getDeviceMasterSlaveMode(&curr_mode);
    std::cout << "当前设备模式: " << curr_mode << std::endl;
    // 设置曝光参数
    if (!m_config.ae_para.auto_ae) {
        m_device->setColorExposureGain(m_config.ae_para.exposure_time, m_config.ae_para.gain);
    } else {
        m_device->enableColorAutoExposure();
    }
// // 设置深度信任度阈值
//     std::cout << "设置深度信任度阈值..." << std::endl;
//     int32_t depthConfidenceResult = m_device->setDepthConfidence(3);
//     std::cout << "setDepthConfidence(2) 返回值: " << depthConfidenceResult << std::endl;
// // 设置深度自动曝光增益范围
//     std::cout << "设置深度自动曝光增益范围..." << std::endl;
//     int32_t depthRangeResult = m_device->setDepthAEGainRange(600, 1000);
//     std::cout << "setDepthAEGainRange(600, 1000) 返回值: " << depthRangeResult << std::endl;

    

    // // 设置深度电流值
    // std::cout << "设置深度电流值..." << std::endl;
    // int32_t depthElectricCurrentResult = m_device->setDepthElectricCurrent(2000);
    // std::cout << "setDepthElectricCurrent(800) 返回值: " << depthElectricCurrentResult << std::endl;
    
    // // 设置深度曝光值
    // std::cout << "设置深度曝光值..." << std::endl;
    // int32_t depthExposureResult = m_device->setDepthExposure(20);
    // std::cout << "setDepthExposure(1000) 返回值: " << depthExposureResult << std::endl;
    
    // // 设置深度增益值
    // std::cout << "设置深度增益值..." << std::endl;
    // int32_t depthGainResult = m_device->setDepthGain(4);
    // std::cout << "setDepthGain(600) 返回值: " << depthGainResult << std::endl;

    // m_device->setMaxDepthValue(1000);
    
    // 设置深度自动曝光状态
    std::cout << "设置深度自动曝光状态..." << std::endl;
    int32_t depthAEResult = m_device->setDepthAEStatus(m_config.ae_para.auto_ae);
    std::cout << "setDepthAEStatus(" << (m_config.ae_para.auto_ae ? "true" : "false") << ") 返回值: " << depthAEResult << std::endl;
    
    std::cout << "相机初始化完成" << std::endl;
    return true;
}

bool BerxelCameraTest::start() {
    if (!m_device) {
        std::cerr << "设备未初始化" << std::endl;
        return false;
    }
    
    std::cout << "启动相机流..." << std::endl;
    
    // 启动彩色流和深度流
    int ret = m_device->startStreams(berxel::BERXEL_HAWK_DEPTH_STREAM | berxel::BERXEL_HAWK_COLOR_STREAM, 
                                    BerxelCameraTest::frame_callback, 
                                    this);
    
    if (ret != 0) {
        std::cerr << "启动Berxel流失败: " << ret << std::endl;
        return false;
    }
    
    m_is_running = true;
    std::cout << "相机流启动成功" << std::endl;
    
    return true;
}

void BerxelCameraTest::stop() {
    if (m_device && m_is_running) {
        std::cout << "停止相机流..." << std::endl;
        
        try {
            // 尝试停止流，如果失败也不要崩溃
            int32_t result = m_device->stopStreams(berxel::BERXEL_HAWK_COLOR_STREAM | berxel::BERXEL_HAWK_DEPTH_STREAM);
            if (result != 0) {
                std::cerr << "警告: 停止相机流返回错误码: " << result << std::endl;
            }
        } catch (const std::exception& e) {
            std::cerr << "停止相机流时发生异常: " << e.what() << std::endl;
        }
        
        m_is_running = false;
        std::cout << "相机流已停止" << std::endl;
        
        // 给设备一些时间来完成停止操作
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

void BerxelCameraTest::set_frame_callback(FrameCallback callback) {
    m_frame_callback = callback;
}

void BerxelCameraTest::frame_callback(berxel::BerxelHawkStreamType streamType,
                                     berxel::BerxelHawkFrame* pFrame,
                                     void* pUserData) {    
    BerxelCameraTest* camera = static_cast<BerxelCameraTest*>(pUserData);
    
        std::lock_guard<std::mutex> lock(camera->m_frame_mutex);
        
        camera->m_frame_count = (uint32_t)pFrame->getFrameIndex();
        
        static std::vector<RGB888> temp_color_frame;
        static std::vector<uint16_t> temp_depth_frame;
        static int frame_width = 0, frame_height = 0;
        static int64_t color_timestamp = -1, depth_timestamp = -1;
        
        if (streamType == berxel::BERXEL_HAWK_COLOR_STREAM) {
            // 处理彩色帧 - 只保存原始数据
            void* rawData = pFrame->getData();
            
            RGB888* rgbData = static_cast<RGB888*>(rawData);
            int width = pFrame->getWidth();
            int height = pFrame->getHeight();
            
            color_timestamp = pFrame->getTimeStamp();
            
            // 使用spdlog打印收到彩色图像数据的时间和时间戳参数
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;
            
            char time_str[100];
            std::strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", std::localtime(&time_t));
            spdlog::info("收到彩色图像数据 - 当前时间: {}.{:03d}, 流类型: {}, 图像时间戳: {}",
                        time_str, ms.count(), streamType, color_timestamp);
            
            // 更新帧尺寸
            camera->m_frame_width = width;
            camera->m_frame_height = height;
            frame_width = width;
            frame_height = height;
            
            // 保存彩色帧数据到临时缓存
            size_t colorSize = static_cast<size_t>(width) * static_cast<size_t>(height);
            
            temp_color_frame.resize(colorSize);
            memcpy(temp_color_frame.data(), rgbData, colorSize * sizeof(RGB888));
        }
        else if (streamType == berxel::BERXEL_HAWK_DEPTH_STREAM) {
            // 处理深度帧 - 只保存原始数据
            void* rawData = pFrame->getData();
            uint16_t* depthData = static_cast<uint16_t*>(rawData);
            int width = pFrame->getWidth();
            int height = pFrame->getHeight();
             
            depth_timestamp = pFrame->getTimeStamp();
            
            // 使用spdlog打印收到深度图像数据的时间和时间戳参数
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;
            
            char time_str[100];
            std::strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", std::localtime(&time_t));
            spdlog::info("收到深度图像数据 - 当前时间: {}.{:03d}, 流类型: {}, 图像时间戳: {}",
                        time_str, ms.count(), streamType, depth_timestamp);
            
            // 更新帧尺寸
            if (frame_width == 0) {
                frame_width = width;
                frame_height = height;
            }
            
            size_t depthSize = static_cast<size_t>(width) * static_cast<size_t>(height);
            
            temp_depth_frame.resize(depthSize);
            memcpy(temp_depth_frame.data(), depthData, depthSize * sizeof(uint16_t));
        }
        
        // 当彩色和深度数据来自同一帧时，添加到处理队列
        if (color_timestamp == depth_timestamp && frame_width > 0 && frame_height > 0) {
            camera->add_frame_to_queue(camera->m_frame_count, temp_color_frame, temp_depth_frame,
                                    frame_width, frame_height);
        } else if (color_timestamp != depth_timestamp) {
            std::cout << "错误：彩色帧和深度帧时间戳不匹配 - 彩色帧时间戳: " << color_timestamp << ", 深度帧时间戳: " << depth_timestamp << std::endl;
        }
        
        // 调用用户回调函数
        if (camera->m_frame_callback) {
            camera->m_frame_callback(streamType, pFrame, pUserData);
        }
        

        camera->m_device->releaseFrame(pFrame);
         
}

uint8_t BerxelCameraTest::rgb_to_gray(const RGB888& rgb) {
    // 使用标准的RGB到灰度转换公式
    return static_cast<uint8_t>(rgb.r * 0.299 + rgb.g * 0.587 + rgb.b * 0.114);
}

bool BerxelCameraTest::get_gray_image(uint8_t* gray_data, int& width, int& height) {
    std::lock_guard<std::mutex> lock(m_frame_mutex);
    
    if (m_latest_gray_frame.empty()) {
        return false;
    }
    
    width = m_frame_width;
    height = m_frame_height;
    
    if (gray_data) {
        memcpy(gray_data, m_latest_gray_frame.data(), m_latest_gray_frame.size());
    }
    
    return true;
}

bool BerxelCameraTest::get_color_image(RGB888* color_data, int& width, int& height) {
    std::lock_guard<std::mutex> lock(m_frame_mutex);
    
    if (m_latest_color_frame.empty()) {
        return false;
    }
    
    width = m_frame_width;
    height = m_frame_height;
    
    if (color_data) {
        memcpy(color_data, m_latest_color_frame.data(), 
               m_latest_color_frame.size() * sizeof(RGB888));
    }
    
    return true;
}

bool BerxelCameraTest::get_depth_image(uint16_t* depth_data, int& width, int& height) {
    std::lock_guard<std::mutex> lock(m_frame_mutex);
    
    if (m_latest_depth_frame.empty()) {
        return false;
    }
    
    width = m_frame_width;
    height = m_frame_height;
    
    if (depth_data) {
        memcpy(depth_data, m_latest_depth_frame.data(), 
               m_latest_depth_frame.size() * sizeof(uint16_t));
    }
    
    return true;
}

bool BerxelCameraTest::save_gray_image_bmp(const std::string& filename, 
                                       const uint8_t* data, 
                                       int width, int height) {
    std::ofstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "无法打开文件: " << filename << std::endl;
        return false;
    }
    
    // 计算行字节数（4字节对齐）
    int rowSize = ((width + 3) / 4) * 4;
    int imageSize = rowSize * height;
    int fileSize = sizeof(BMPFILEHEADER) + sizeof(BMPINFOHEADER) + 256 * 4 + imageSize; // 包含调色板
    
    // BMP文件头
    BMPFILEHEADER fileHeader = {0};
    fileHeader.bfType = 0x4D42; // 'BM'
    fileHeader.bfSize = fileSize;
    fileHeader.bfReserved1 = 0;
    fileHeader.bfReserved2 = 0;
    fileHeader.bfOffBits = sizeof(BMPFILEHEADER) + sizeof(BMPINFOHEADER) + 256 * 4;
    
    // BMP信息头
    BMPINFOHEADER infoHeader = {0};
    infoHeader.biSize = sizeof(BMPINFOHEADER);
    infoHeader.biWidth = width;
    infoHeader.biHeight = height; // 正值表示从下到上
    infoHeader.biPlanes = 1;
    infoHeader.biBitCount = 8; // 8位灰度
    infoHeader.biCompression = 0; // 无压缩
    infoHeader.biSizeImage = imageSize;
    infoHeader.biXPelsPerMeter = 0;
    infoHeader.biYPelsPerMeter = 0;
    infoHeader.biClrUsed = 256;
    infoHeader.biClrImportant = 0;
    
    // 写入文件头和信息头
    file.write(reinterpret_cast<const char*>(&fileHeader), sizeof(fileHeader));
    file.write(reinterpret_cast<const char*>(&infoHeader), sizeof(infoHeader));
    
    // 写入调色板（灰度）
    for (int i = 0; i < 256; i++) {
        uint8_t color[4] = {static_cast<uint8_t>(i), static_cast<uint8_t>(i), static_cast<uint8_t>(i), 0};
        file.write(reinterpret_cast<const char*>(color), 4);
    }
    
    // 写入图像数据（从下到上，左到右）
    std::vector<uint8_t> rowBuffer(rowSize, 0);
    for (int y = height - 1; y >= 0; y--) {
        // 复制一行数据
        memcpy(rowBuffer.data(), &data[y * width], width);
        file.write(reinterpret_cast<const char*>(rowBuffer.data()), rowSize);
    }
    
    file.close();
    std::cout << "灰度BMP图像已保存到: " << filename << std::endl;
    return true;
}

bool BerxelCameraTest::save_color_image_bmp(const std::string& filename, 
                                         const RGB888* data, 
                                         int width, int height) {
    std::ofstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "无法打开文件: " << filename << std::endl;
        return false;
    }
    
    // 计算行字节数（4字节对齐）
    int rowSize = ((width * 3 + 3) / 4) * 4;
    int imageSize = rowSize * height;
    int fileSize = sizeof(BMPFILEHEADER) + sizeof(BMPINFOHEADER) + imageSize;
    
    // BMP文件头
    BMPFILEHEADER fileHeader = {0};
    fileHeader.bfType = 0x4D42; // 'BM'
    fileHeader.bfSize = fileSize;
    fileHeader.bfReserved1 = 0;
    fileHeader.bfReserved2 = 0;
    fileHeader.bfOffBits = sizeof(BMPFILEHEADER) + sizeof(BMPINFOHEADER);
    
    // BMP信息头
    BMPINFOHEADER infoHeader = {0};
    infoHeader.biSize = sizeof(BMPINFOHEADER);
    infoHeader.biWidth = width;
    infoHeader.biHeight = height; // 正值表示从下到上
    infoHeader.biPlanes = 1;
    infoHeader.biBitCount = 24; // 24位彩色
    infoHeader.biCompression = 0; // 无压缩
    infoHeader.biSizeImage = imageSize;
    infoHeader.biXPelsPerMeter = 0;
    infoHeader.biYPelsPerMeter = 0;
    infoHeader.biClrUsed = 0;
    infoHeader.biClrImportant = 0;
    
    // 写入文件头和信息头
    file.write(reinterpret_cast<const char*>(&fileHeader), sizeof(fileHeader));
    file.write(reinterpret_cast<const char*>(&infoHeader), sizeof(infoHeader));
    
    // 写入图像数据（从下到上，左到右，BGR格式）
    std::vector<uint8_t> rowBuffer(rowSize, 0);
    for (int y = height - 1; y >= 0; y--) {
        for (int x = 0; x < width; x++) {
            const RGB888& pixel = data[y * width + x];
            // BMP使用BGR格式
            rowBuffer[x * 3 + 0] = pixel.b;
            rowBuffer[x * 3 + 1] = pixel.g;
            rowBuffer[x * 3 + 2] = pixel.r;
        }
        file.write(reinterpret_cast<const char*>(rowBuffer.data()), rowSize);
    }
    
    file.close();
    std::cout << "彩色BMP图像已保存到: " << filename << std::endl;
    return true;
}

bool BerxelCameraTest::save_depth_image_bmp(const std::string& filename, 
                                         const uint16_t* data, 
                                         int width, int height) {
    if (!data || width <= 0 || height <= 0) {
        std::cerr << "无效的深度图像数据" << std::endl;
        return false;
    }
    
    std::ofstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "无法创建文件: " << filename << std::endl;
        return false;
    }
    
    // 计算行大小（4字节对齐）
    int rowSize = ((width * 3 + 3) / 4) * 4;
    int imageSize = rowSize * height;
    
    // BMP文件头
    BMPFILEHEADER fileHeader = {0};
    fileHeader.bfType = 0x4D42; // "BM"
    fileHeader.bfSize = sizeof(BMPFILEHEADER) + sizeof(BMPINFOHEADER) + imageSize;
    fileHeader.bfOffBits = sizeof(BMPFILEHEADER) + sizeof(BMPINFOHEADER);
    
    // BMP信息头
    BMPINFOHEADER infoHeader = {0};
    infoHeader.biSize = sizeof(BMPINFOHEADER);
    infoHeader.biWidth = width;
    infoHeader.biHeight = height; // 正值表示从下到上
    infoHeader.biPlanes = 1;
    infoHeader.biBitCount = 24; // 24位彩色（用于深度可视化）
    infoHeader.biCompression = 0; // 无压缩
    infoHeader.biSizeImage = imageSize;
    infoHeader.biXPelsPerMeter = 0;
    infoHeader.biYPelsPerMeter = 0;
    infoHeader.biClrUsed = 0;
    infoHeader.biClrImportant = 0;
    
    // 写入文件头和信息头
    file.write(reinterpret_cast<const char*>(&fileHeader), sizeof(fileHeader));
    file.write(reinterpret_cast<const char*>(&infoHeader), sizeof(infoHeader));
    
    // 找到深度数据的最大值和最小值用于归一化
    uint16_t minDepth = 65535, maxDepth = 0;
    for (int i = 0; i < width * height; i++) {
        if (data[i] > 0) { // 忽略无效深度值
            if (data[i] < minDepth) minDepth = data[i];
            if (data[i] > maxDepth) maxDepth = data[i];
        }
    }
    
    float depthRange = (maxDepth > minDepth) ? (maxDepth - minDepth) : 1.0f;
    
    // 写入图像数据（从下到上，左到右，将深度值转换为彩色可视化）
    std::vector<uint8_t> rowBuffer(rowSize, 0);
    for (int y = height - 1; y >= 0; y--) {
        for (int x = 0; x < width; x++) {
            uint16_t depthValue = data[y * width + x];
            uint8_t intensity = 0;
            
            if (depthValue > 0) {
                // 将深度值归一化到0-255范围
                intensity = static_cast<uint8_t>(((depthValue - minDepth) / depthRange) * 255);
            }
            
            // 使用灰度值填充BGR
            rowBuffer[x * 3 + 0] = intensity; // B
            rowBuffer[x * 3 + 1] = intensity; // G
            rowBuffer[x * 3 + 2] = intensity; // R
        }
        file.write(reinterpret_cast<const char*>(rowBuffer.data()), rowSize);
    }
    
    file.close();
    std::cout << "深度BMP图像已保存到: " << filename 
              << " (深度范围: " << minDepth << "-" << maxDepth << ")" << std::endl;
    return true;
}

bool BerxelCameraTest::get_camera_info(std::string& info) {
    if (!m_device) {
        return false;
    }
    
    info = "Berxel Camera Info:\n";
    info += "  Address: " + std::string(m_device_info.deviceAddress) + "\n";
    info += "  Serial: " + std::string(m_device_info.serialNumber) + "\n";
    info += "  Product ID: " + std::to_string(m_device_info.productId) + "\n";
    info += "  Frame Size: " + std::to_string(m_frame_width) + "x" + std::to_string(m_frame_height) + "\n";
    info += "  Frame Rate: " + std::to_string(m_config.frame_rate) + "\n";
    info += "  Frame Count: " + std::to_string(m_frame_count) + "\n";
    
    return true;
}

bool BerxelCameraTest::load_config() {
    // 这里可以从配置文件加载参数
    // 目前使用默认配置
    return true;
}

// 基于平面拟合的深度图像修复
void BerxelCameraTest::plane_fitting_repair(const uint16_t* input_data, uint16_t* output_data, 
                                          int width, int height, int window_size, double max_distance) {
    if (!input_data || !output_data || width <= 0 || height <= 0) {
        std::cerr << "平面拟合修复: 无效的输入参数" << std::endl;
        return;
    }
    
    // 确保窗口大小为奇数且至少为3
    if (window_size < 3) window_size = 3;
    if (window_size % 2 == 0) window_size++;
    
    int half_window = window_size / 2;
    
    std::cout << "开始平面拟合修复: 窗口大小=" << window_size 
              << ", 最大距离阈值=" << max_distance << "mm" << std::endl;
    
    // 首先复制原始数据
    memcpy(output_data, input_data, width * height * sizeof(uint16_t));
    
    int repaired_pixels = 0;
    int total_invalid_pixels = 0;
    
    // 遍历每个像素
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            int idx = y * width + x;
            
            // 如果当前像素无效（深度值为0或异常值）
            if (input_data[idx] == 0) {
                total_invalid_pixels++;
                
                // 收集窗口内的有效深度值
                std::vector<double> valid_depths;
                std::vector<int> valid_x, valid_y;
                
                for (int dy = -half_window; dy <= half_window; dy++) {
                    for (int dx = -half_window; dx <= half_window; dx++) {
                        int nx = x + dx;
                        int ny = y + dy;
                        
                        if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                            int nidx = ny * width + nx;
                            if (input_data[nidx] > 0) {
                                valid_depths.push_back(static_cast<double>(input_data[nidx]));
                                valid_x.push_back(nx);
                                valid_y.push_back(ny);
                            }
                        }
                    }
                }
                
                // 如果有足够的有效点进行平面拟合（至少需要3个点）
                if (valid_depths.size() >= 3) {
                    // 使用最小二乘法拟合平面: z = ax + by + c
                    double sum_x = 0, sum_y = 0, sum_z = 0;
                    double sum_xx = 0, sum_yy = 0, sum_xy = 0;
                    double sum_xz = 0, sum_yz = 0;
                    int n = valid_depths.size();
                    
                    for (int i = 0; i < n; i++) {
                        double px = valid_x[i];
                        double py = valid_y[i];
                        double pz = valid_depths[i];
                        
                        sum_x += px;
                        sum_y += py;
                        sum_z += pz;
                        sum_xx += px * px;
                        sum_yy += py * py;
                        sum_xy += px * py;
                        sum_xz += px * pz;
                        sum_yz += py * pz;
                    }
                    
                    // 构建法方程组并求解
                    double det = n * (sum_xx * sum_yy - sum_xy * sum_xy) - 
                                sum_x * (sum_x * sum_yy - sum_xy * sum_y) + 
                                sum_y * (sum_x * sum_xy - sum_xx * sum_y);
                    
                    if (std::fabs(det) > 1e-10) {  // 避免奇异矩阵
                        double a = (n * (sum_xz * sum_yy - sum_yz * sum_xy) - 
                                   sum_x * (sum_z * sum_yy - sum_yz * sum_y) + 
                                   sum_y * (sum_z * sum_xy - sum_xz * sum_y)) / det;
                        
                        double b = (n * (sum_xx * sum_yz - sum_xz * sum_xy) - 
                                   sum_x * (sum_x * sum_yz - sum_xz * sum_y) + 
                                   sum_y * (sum_x * sum_z - sum_xx * sum_z)) / det;
                        
                        double c = (sum_z - a * sum_x - b * sum_y) / n;
                        
                        // 计算拟合的深度值
                        double fitted_depth = a * x + b * y + c;
                        
                        // 验证拟合结果的合理性
                        if (fitted_depth > 0 && fitted_depth < 65535) {
                            // 计算拟合平面与周围有效点的平均距离
                            double avg_distance = 0;
                            for (int i = 0; i < n; i++) {
                                double predicted = a * valid_x[i] + b * valid_y[i] + c;
                                avg_distance += std::fabs(predicted - valid_depths[i]);
                            }
                            avg_distance /= n;
                            
                            // 如果平均距离在合理范围内，使用拟合值
                            if (avg_distance <= max_distance) {
                                output_data[idx] = static_cast<uint16_t>(fitted_depth);
                                repaired_pixels++;
                            }
                        }
                    }
                } else if (valid_depths.size() > 0) {
                    // 如果有效点不足以拟合平面，使用平均值
                    double avg_depth = 0;
                    for (double depth : valid_depths) {
                        avg_depth += depth;
                    }
                    avg_depth /= valid_depths.size();
                    
                    output_data[idx] = static_cast<uint16_t>(avg_depth);
                    repaired_pixels++;
                }
            }
        }
    }
    
    std::cout << "平面拟合修复完成: 总无效像素=" << total_invalid_pixels 
              << ", 成功修复=" << repaired_pixels 
              << ", 修复率=" << (total_invalid_pixels > 0 ? (100.0 * repaired_pixels / total_invalid_pixels) : 0) << "%" << std::endl;
}

// 深度图像直方图均衡化处理
void BerxelCameraTest::histogram_equalization_depth(const uint16_t* input_data, uint16_t* output_data, 
                                                  int width, int height, uint16_t min_depth, uint16_t max_depth) {
    if (!input_data || !output_data || width <= 0 || height <= 0) {
        std::cerr << "直方图均衡化: 无效的输入参数" << std::endl;
        return;
    }
    
    int total_pixels = width * height;
    
    // 如果没有指定深度范围，自动计算有效深度范围
    if (min_depth == 0 && max_depth == 65535) {
        min_depth = 65535;
        max_depth = 0;
        
        // 找到有效深度值的范围（忽略0值）
        for (int i = 0; i < total_pixels; i++) {
            if (input_data[i] > 0) {
                if (input_data[i] < min_depth) min_depth = input_data[i];
                if (input_data[i] > max_depth) max_depth = input_data[i];
            }
        }
        
        if (min_depth >= max_depth) {
            std::cerr << "直方图均衡化: 无效的深度范围" << std::endl;
            // 直接复制原始数据
            memcpy(output_data, input_data, total_pixels * sizeof(uint16_t));
            return;
        }
    }
    
    // 计算有效深度范围
    int depth_range = max_depth - min_depth + 1;
    
    // 创建直方图数组
    std::vector<int> histogram(depth_range, 0);
    int valid_pixels = 0;
    
    // 统计直方图（只统计有效深度值）
    for (int i = 0; i < total_pixels; i++) {
        uint16_t depth = input_data[i];
        if (depth >= min_depth && depth <= max_depth) {
            histogram[depth - min_depth]++;
            valid_pixels++;
        }
    }
    
    if (valid_pixels == 0) {
        std::cerr << "直方图均衡化: 没有有效的深度像素" << std::endl;
        memcpy(output_data, input_data, total_pixels * sizeof(uint16_t));
        return;
    }
    
    // 计算累积分布函数 (CDF)
    std::vector<int> cdf(depth_range, 0);
    cdf[0] = histogram[0];
    for (int i = 1; i < depth_range; i++) {
        cdf[i] = cdf[i-1] + histogram[i];
    }
    
    // 找到第一个非零的CDF值（用于CDF归一化）
    int cdf_min = 0;
    for (int i = 0; i < depth_range; i++) {
        if (cdf[i] > 0) {
            cdf_min = cdf[i];
            break;
        }
    }
    
    // 创建映射表
    std::vector<uint16_t> mapping_table(depth_range);
    for (int i = 0; i < depth_range; i++) {
        if (cdf[i] > 0) {
            // 直方图均衡化公式: new_value = ((cdf[i] - cdf_min) / (valid_pixels - cdf_min)) * (max_depth - min_depth) + min_depth
            double normalized = static_cast<double>(cdf[i] - cdf_min) / (valid_pixels - cdf_min);
            mapping_table[i] = static_cast<uint16_t>(normalized * (max_depth - min_depth) + min_depth);
        } else {
            mapping_table[i] = min_depth;
        }
    }
    
    // 应用映射表到输出图像
    for (int i = 0; i < total_pixels; i++) {
        uint16_t depth = input_data[i];
        if (depth >= min_depth && depth <= max_depth) {
            output_data[i] = mapping_table[depth - min_depth];
        } else {
            // 保持无效深度值不变
            output_data[i] = depth;
        }
    }
    
    std::cout << "直方图均衡化完成: 深度范围 [" << min_depth << ", " << max_depth 
              << "], 有效像素数: " << valid_pixels << "/" << total_pixels << std::endl;
}

// 保存直方图均衡化后的深度图像
bool BerxelCameraTest::save_hist_equalized_depth_image_bmp(const std::string& filename,
                                                      const uint16_t* depth_data,
                                                      int width, int height) {
    if (!depth_data || width <= 0 || height <= 0) {
        std::cerr << "无效的深度图像数据" << std::endl;
        return false;
    }
    
    // 创建输出缓冲区用于直方图均衡化
    std::vector<uint16_t> equalized_data(width * height);
    
    // 直接应用直方图均衡化
    histogram_equalization_depth(depth_data, equalized_data.data(), width, height);
    
    // 保存均衡化后的图像
    std::ofstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "无法创建文件: " << filename << std::endl;
        return false;
    }
    
    // 计算行大小（4字节对齐）
    int rowSize = ((width * 3 + 3) / 4) * 4;
    int imageSize = rowSize * height;
    
    // BMP文件头
    BMPFILEHEADER fileHeader = {0};
    fileHeader.bfType = 0x4D42; // "BM"
    fileHeader.bfSize = sizeof(BMPFILEHEADER) + sizeof(BMPINFOHEADER) + imageSize;
    fileHeader.bfOffBits = sizeof(BMPFILEHEADER) + sizeof(BMPINFOHEADER);
    
    // BMP信息头
    BMPINFOHEADER infoHeader = {0};
    infoHeader.biSize = sizeof(BMPINFOHEADER);
    infoHeader.biWidth = width;
    infoHeader.biHeight = height;
    infoHeader.biPlanes = 1;
    infoHeader.biBitCount = 24;
    infoHeader.biCompression = 0;
    infoHeader.biSizeImage = imageSize;
    infoHeader.biXPelsPerMeter = 0;
    infoHeader.biYPelsPerMeter = 0;
    infoHeader.biClrUsed = 0;
    infoHeader.biClrImportant = 0;
    
    // 写入文件头和信息头
    file.write(reinterpret_cast<const char*>(&fileHeader), sizeof(fileHeader));
    file.write(reinterpret_cast<const char*>(&infoHeader),  sizeof(infoHeader));
    
    // 找到均衡化后数据的最大值和最小值用于可视化
    uint16_t minDepth = 65535, maxDepth = 0;
    for (int i = 0; i < width * height; i++) {
        if (equalized_data[i] > 0) {
            if (equalized_data[i] < minDepth) minDepth = equalized_data[i];
            if (equalized_data[i] > maxDepth) maxDepth = equalized_data[i];
        }
    }
    
    float depthRange = (maxDepth > minDepth) ? (maxDepth - minDepth) : 1.0f;
    
    // 写入图像数据（从下到上，左到右）
    std::vector<uint8_t> rowBuffer(rowSize, 0);
    for (int y = height - 1; y >= 0; y--) {
        for (int x = 0; x < width; x++) {
            uint16_t depthValue = equalized_data[y * width + x];
            uint8_t intensity = 0;
            
            if (depthValue > 0) {
                // 将深度值归一化到0-255范围
                intensity = static_cast<uint8_t>(((depthValue - minDepth) / depthRange) * 255);
            }
            
            // 使用灰度值填充BGR
            rowBuffer[x * 3 + 0] = intensity; // B
            rowBuffer[x * 3 + 1] = intensity; // G
            rowBuffer[x * 3 + 2] = intensity; // R
        }
        file.write(reinterpret_cast<const char*>(rowBuffer.data()), rowSize);
    }
    
    file.close();
    std::cout << "直方图均衡化深度BMP图像已保存到: " << filename 
              << " (均衡化后深度范围: " << minDepth << "-" << maxDepth << ")" << std::endl;
    return true;
}
// ==================== 彩色图像高斯模糊处理方法实现 ====================

// 彩色图像高斯模糊处理
void BerxelCameraTest::gaussian_blur_color(const RGB888* input_data, RGB888* output_data, 
                                         int width, int height, double sigma, int kernel_size) {
    if (!input_data || !output_data || width <= 0 || height <= 0) {
        std::cerr << "高斯模糊处理: 无效的输入参数" << std::endl;
        return;
    }
    
    // 确保核大小为奇数
    if (kernel_size % 2 == 0) {
        kernel_size++;
    }
    
    int half_kernel = kernel_size / 2;
    
    // 生成高斯核
    std::vector<std::vector<double>> kernel(kernel_size, std::vector<double>(kernel_size));
    double sum = 0.0;
    double sigma_sq = sigma * sigma;
    
    for (int i = 0; i < kernel_size; i++) {
        for (int j = 0; j < kernel_size; j++) {
            int x = i - half_kernel;
            int y = j - half_kernel;
            double value = exp(-(x * x + y * y) / (2.0 * sigma_sq)) / (2.0 * M_PI * sigma_sq);
            kernel[i][j] = value;
            sum += value;
        }
    }
    
    // 归一化核
    for (int i = 0; i < kernel_size; i++) {
        for (int j = 0; j < kernel_size; j++) {
            kernel[i][j] /= sum;
        }
    }
    
    // 应用高斯模糊
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            double r_sum = 0.0, g_sum = 0.0, b_sum = 0.0;
            
            for (int ky = 0; ky < kernel_size; ky++) {
                for (int kx = 0; kx < kernel_size; kx++) {
                    int px = x + kx - half_kernel;
                    int py = y + ky - half_kernel;
                    
                    // 边界处理：镜像填充
                    if (px < 0) px = -px;
                    if (px >= width) px = 2 * width - px - 1;
                    if (py < 0) py = -py;
                    if (py >= height) py = 2 * height - py - 1;
                    
                    const RGB888& pixel = input_data[py * width + px];
                    double weight = kernel[ky][kx];
                    
                    r_sum += pixel.r * weight;
                    g_sum += pixel.g * weight;
                    b_sum += pixel.b * weight;
                }
            }
            
            RGB888& output_pixel = output_data[y * width + x];
            output_pixel.r = static_cast<uint8_t>(std::min(255.0, std::max(0.0, r_sum)));
            output_pixel.g = static_cast<uint8_t>(std::min(255.0, std::max(0.0, g_sum)));
            output_pixel.b = static_cast<uint8_t>(std::min(255.0, std::max(0.0, b_sum)));
        }
    }
}

// 保存高斯模糊后的彩色图像
bool BerxelCameraTest::save_gaussian_blurred_color_image_bmp(const std::string& filename, const RGB888* color_data,
                                                           int width, int height, double sigma, int kernel_size) {
    if (!color_data || width <= 0 || height <= 0) {
        std::cerr << "保存高斯模糊彩色图像: 无效的输入参数" << std::endl;
        return false;
    }
    
    std::cout << "开始对彩色图像进行高斯模糊处理 (sigma=" << sigma << ", kernel_size=" << kernel_size << ")..." << std::endl;
    
    // 分配输出缓冲区
    std::vector<RGB888> blurred_data(width * height);
    
    // 执行高斯模糊
    gaussian_blur_color(color_data, blurred_data.data(), width, height, sigma, kernel_size);
    
    // 保存为BMP文件
    std::ofstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "无法创建文件: " << filename << std::endl;
        return false;
    }
    
    // BMP文件头
    struct BMPHeader {
        uint16_t type = 0x4D42;        // "BM"
        uint32_t size;                 // 文件大小
        uint16_t reserved1 = 0;
        uint16_t reserved2 = 0;
        uint32_t offset = 54;          // 数据偏移
    } __attribute__((packed));
    
    // BMP信息头
    struct BMPInfoHeader {
        uint32_t size = 40;            // 信息头大小
        int32_t width;                 // 图像宽度
        int32_t height;                // 图像高度
        uint16_t planes = 1;           // 颜色平面数
        uint16_t bits_per_pixel = 24;  // 每像素位数
        uint32_t compression = 0;      // 压缩类型
        uint32_t image_size = 0;       // 图像大小
        int32_t x_pixels_per_meter = 0;
        int32_t y_pixels_per_meter = 0;
        uint32_t colors_used = 0;
        uint32_t important_colors = 0;
    } __attribute__((packed));
    
    // 计算行字节数（4字节对齐）
    int row_padded = (width * 3 + 3) & (~3);
    
    BMPHeader header;
    header.size = 54 + row_padded * height;
    
    BMPInfoHeader info_header;
    info_header.width = width;
    info_header.height = height;
    info_header.image_size = row_padded * height;
    
    // 写入文件头
    file.write(reinterpret_cast<const char*>(&header), sizeof(header));
    file.write(reinterpret_cast<const char*>(&info_header), sizeof(info_header));
    
    // 写入像素数据（BMP格式为BGR，从下到上）
    std::vector<uint8_t> row_data(row_padded, 0);
    for (int y = height - 1; y >= 0; y--) {
        for (int x = 0; x < width; x++) {
            const RGB888& pixel = blurred_data[y * width + x];
            row_data[x * 3 + 0] = pixel.b;     // B
            row_data[x * 3 + 1] = pixel.g;     // G
            row_data[x * 3 + 2] = pixel.r;     // R
        }
        file.write(reinterpret_cast<const char*>(row_data.data()), row_padded);
    }
    
    file.close();
    
    std::cout << "高斯模糊彩色图像已保存到: " << filename 
              << " (尺寸: " << width << "x" << height 
              << ", sigma=" << sigma << ", kernel_size=" << kernel_size << ")" << std::endl;
    return true;
}

// ==================== 灰度物品检测方法实现 ====================

// 配置灰度物品检测参数
void BerxelCameraTest::configure_gray_detection(const point_pos_2d valid_coords[4], uint8_t gray_mask, uint32_t area_mask) {
    std::lock_guard<std::mutex> lock(m_detection_mutex);
    
    // 复制检测区域坐标
    for (int i = 0; i < 4; i++) {
        m_detection_coords[i] = valid_coords[i];
    }
    
    m_gray_mask = gray_mask;
    m_area_mask = area_mask;
    
    std::cout << "灰度物品检测参数已配置:" << std::endl;
    std::cout << "  检测区域: [" << m_detection_coords[0].x << "," << m_detection_coords[0].y << "] -> "
              << "[" << m_detection_coords[3].x << "," << m_detection_coords[3].y << "]" << std::endl;
    std::cout << "  灰度阈值偏移: " << (int)m_gray_mask << std::endl;
    std::cout << "  最小检测面积: " << m_area_mask << " 像素" << std::endl;
}

/**
 * 判断点是否在有效检测区域内（简化版本）
 *
 * 图像坐标系说明：x从左到右增，y从上到下增
 *
 * valid_coords 顶点排列顺序说明（图像坐标系）：
 * - 推荐按顺时针顺序排列四个顶点
 * - 矩形区域示例：
 *   valid_coords[0] = 左上角 (x_min, y_min)  // 起始点
 *   valid_coords[1] = 右上角 (x_max, y_min)  // 顺时针下一个点
 *   valid_coords[2] = 右下角 (x_max, y_max)  // 顺时针下一个点
 *   valid_coords[3] = 左下角 (x_min, y_max)  // 顺时针最后一个点
 *
 * - 也支持逆时针排列：
 *   valid_coords[0] = 左上角 (x_min, y_min)
 *   valid_coords[1] = 左下角 (x_min, y_max)
 *   valid_coords[2] = 右下角 (x_max, y_max)
 *   valid_coords[3] = 右上角 (x_max, y_min)
 *
 * @param x 待检测点的x坐标（图像坐标系）
 * @param y 待检测点的y坐标（图像坐标系）
 * @param valid_coords 检测区域的四个顶点坐标数组
 * @return true表示点在区域内，false表示不在区域内
 */
bool BerxelCameraTest::is_position_valid_for_detection(int x, int y, const point_pos_2d* valid_coords) {
    if (!valid_coords) {
        return false;
    }
    
    // 使用射线投射算法判断点是否在多边形内
    bool inside = false;
    
    for (int i = 0, j = 3; i < 4; j = i++) {
        int xi = valid_coords[i].x;
        int yi = valid_coords[i].y;
        int xj = valid_coords[j].x;
        int yj = valid_coords[j].y;
        
        // 检查从点(x,y)向右的射线是否与边相交
        if (((yi > y) != (yj > y)) &&
            (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
            inside = !inside;
        }
    }
    
    return inside;
}

// 计算动态灰度阈值
uint8_t BerxelCameraTest::calculate_dynamic_gray_threshold(berxel::BerxelHawkFrame* pFrame, const point_pos_2d* calib_points) {
    if (!pFrame || pFrame->getStreamType() != berxel::BERXEL_HAWK_COLOR_STREAM) {
        return 128; // 默认阈值
    }
    
    RGB888* rgbImage = (RGB888*)pFrame->getData();
    uint32_t gray_total = 0;
    int width = pFrame->getWidth();
    int height = pFrame->getHeight();
    
    // 计算四个校准点的平均灰度值
    for (int i = 0; i < 4; i++) {
        // if(i != 1)
        {
            uint32_t index = calib_points[i].y * width + calib_points[i].x;
            if (index < (uint32_t)(width * height)) {
                uint8_t gray_value = rgbImage[index].r * 0.299 + 
                                rgbImage[index].g * 0.587 + 
                                rgbImage[index].b * 0.114;
                gray_total += gray_value;
                
                std::cout << "校准点 " << i+1 << " (" << calib_points[i].x << "," 
                        << calib_points[i].y << ") 灰度值: " << (int)gray_value << std::endl;
            }
        }
    }
    
    uint8_t avg_gray = gray_total / 4;
    std::cout << "背景平均灰度值: " << (int)avg_gray << std::endl;
    
    return avg_gray;
}

// 从帧数据计算动态灰度阈值
uint8_t BerxelCameraTest::calculate_dynamic_gray_threshold(const FrameData& frame_data, const point_pos_2d* calib_points) {
    if (frame_data.color_data.empty() || frame_data.width == 0 || frame_data.height == 0) {
        return 128; // 默认阈值
    }
    
    const std::vector<RGB888>& rgbImage = frame_data.color_data;
    uint32_t gray_total = 0;
    int width = frame_data.width;
    int height = frame_data.height;
    int valid_points = 0;
    
    // 计算四个校准点的平均灰度值
    for (int i = 0; i < 4; i++) {
        uint32_t index = calib_points[i].y * width + calib_points[i].x;
        if (index < (uint32_t)(width * height)) {
            uint8_t gray_value = rgb_to_gray(rgbImage[index]);
            gray_total += gray_value;
            valid_points++;
            
            std::cout << "校准点 " << i+1 << " (" << calib_points[i].x << ","
                    << calib_points[i].y << ") 灰度值: " << (int)gray_value << std::endl;
        }
    }
    
    if (valid_points == 0) {
        return 128; // 如果没有有效点，返回默认值
    }
    
    uint8_t avg_gray = gray_total / valid_points;
    std::cout << "背景平均灰度值: " << (int)avg_gray << std::endl;
    
    return avg_gray;
}

// // 执行灰度物品检测
// gray_detection_result BerxelCameraTest::detect_gray_object(berxel::BerxelHawkFrame* pFrame) {
//     gray_detection_result result = {false, 0, 0, 0, 0};
    
//     if (!pFrame || pFrame->getStreamType() != berxel::BERXEL_HAWK_COLOR_STREAM) {
//         return result;
//     }
    
//     RGB888* rgbImage = (RGB888*)pFrame->getData();
//     int width = pFrame->getWidth();
//     int height = pFrame->getHeight();
    
//     // 获取当前检测参数
//     point_pos_2d current_coords[4];
//     uint8_t current_gray_mask;
//     uint32_t current_area_mask;
    
//     {
//         std::lock_guard<std::mutex> lock(m_detection_mutex);
//         for (int i = 0; i < 4; i++) {
//             current_coords[i] = m_detection_coords[i];
//         }
//         current_gray_mask = m_gray_mask;
//         current_area_mask = m_area_mask;
//     }
    
//     // 计算动态灰度阈值
//     uint8_t dynamic_threshold = calculate_dynamic_gray_threshold(pFrame, current_coords);
//     uint8_t final_threshold = dynamic_threshold + current_gray_mask;
    
//     std::cout << "最终检测阈值: " << (int)final_threshold << std::endl;
    
//     // 统计超过阈值的像素数量
//     uint32_t detected_area = 0;
    
//     for (int i = 0; i < width; i++) {
//         for (int j = 0; j < height; j++) {
//             // 检查是否在有效检测区域内
//             if (is_position_valid_for_detection(i, j, current_coords)) {
                
//                 int index = j * width + i;  // 注意索引计算方式
                
//                 // 计算灰度值
//                 uint8_t gray_value = rgbImage[index].r * 0.299 + 
//                                    rgbImage[index].g * 0.587 + 
//                                    rgbImage[index].b * 0.114;
                
//                 // 如果灰度值超过阈值，认为是物品像素
//                 if (gray_value > final_threshold) {
//                     detected_area++;
//                 }
// // std::cout << "位置(" << i << "," << j << ") 灰度值: " << (int)gray_value 
// //                               << ", 背景灰度: " << dynamic_threshold 
// //                               << ", 差值: " << std::abs(gray_value - dynamic_threshold) 
// //                               << ", 阈值: " << final_threshold << std::endl;
                
//             }
//         }
//     }
    
//     std::cout << "检测到的物品像素数量: " << detected_area << std::endl;
    
//     // 填充检测结果
//     result.object_detected = (detected_area >= current_area_mask);
//     result.detected_area = detected_area;
//     result.background_gray = dynamic_threshold;
//     result.detection_threshold = final_threshold;
//     result.frame_index = pFrame->getFrameIndex();
    
//     // 更新最新检测结果
//     {
//         std::lock_guard<std::mutex> lock(m_detection_mutex);
//         m_latest_result = result;
//     }
    
//     return result;
// }


// 配置深度物品检测参数
void BerxelCameraTest::configure_depth_detection(const point_pos_2d valid_coords[4], uint16_t depth_threshold, uint32_t area_mask) {
    std::lock_guard<std::mutex> lock(m_depth_detection_mutex);
    
    // 复制检测区域坐标
    for (int i = 0; i < 4; i++) {
        m_depth_detection_coords[i] = valid_coords[i];
    }
    
    m_depth_threshold = depth_threshold;
    m_depth_area_mask = area_mask;
    
    std::cout << "深度物品检测参数已配置:" << std::endl;
    std::cout << "  检测区域: [" << m_depth_detection_coords[0].x << "," << m_depth_detection_coords[0].y << "] -> "
              << "[" << m_depth_detection_coords[3].x << "," << m_depth_detection_coords[3].y << "]" << std::endl;
    std::cout << "  深度阈值: " << m_depth_threshold << " mm" << std::endl;
    std::cout << "  最小检测面积: " << m_depth_area_mask << " 像素" << std::endl;
}

// 计算动态深度阈值
uint16_t BerxelCameraTest::calculate_dynamic_depth_threshold(const FrameData& frame_data, const point_pos_2d* calib_points) {
    if (frame_data.depth_data.empty() || frame_data.width == 0 || frame_data.height == 0) {
        return 1000; // 默认阈值
    }
    
    const std::vector<uint16_t>& depthImage = frame_data.depth_data;
    uint64_t depth_total = 0;
    uint32_t valid_points = 0;
    int width = frame_data.width;
    int height = frame_data.height;
    
    // 计算四个校准点的平均深度值
    for (int i = 0; i < 4; i++) {
        uint32_t index = calib_points[i].y * width + calib_points[i].x;
        if (index < (uint32_t)(width * height)) {
            uint16_t depth_value = depthImage[index];
            if (depth_value > 0) { // 忽略无效深度值
                depth_total += depth_value;
                valid_points++;
                
                std::cout << "校准点 " << i+1 << " (" << calib_points[i].x << "," 
                        << calib_points[i].y << ") 深度值: " << depth_value << " mm" << std::endl;
            } else {
                std::cout << "校准点 " << i+1 << " (" << calib_points[i].x << "," 
                        << calib_points[i].y << ") 深度值无效 (0 mm)" << std::endl;
            }
        } else {
            std::cout << "警告: 校准点 " << i+1 << " (" << calib_points[i].x << "," 
                    << calib_points[i].y << ") 索引越界，跳过该点" << std::endl;
        }
    }
    
    // 如果没有有效的深度点，返回默认值
    if (valid_points == 0) {
        std::cout << "警告: 校准点没有有效深度值，使用默认深度阈值" << std::endl;
        return 1000;
    }
    
    uint16_t avg_depth = depth_total / valid_points;
    std::cout << "背景平均深度值: " << avg_depth << " mm" << std::endl;
    
    return avg_depth;
}

// // 执行深度物品检测
// depth_detection_result BerxelCameraTest::detect_depth_object(berxel::BerxelHawkFrame* pFrame) {
//     depth_detection_result result = {false, 0, 0, 0, 0, 65535, 0, 0};
    
//     if (!pFrame || pFrame->getStreamType() != berxel::BERXEL_HAWK_DEPTH_STREAM) {
//         return result;
//     }
    
//     uint16_t* depthImage = (uint16_t*)pFrame->getData();
//     int width = pFrame->getWidth();
//     int height = pFrame->getHeight();
    
//     // 获取当前检测参数
//     point_pos_2d current_coords[4];
//     uint16_t current_depth_threshold;
//     uint32_t current_area_mask;
    
//     {
//         std::lock_guard<std::mutex> lock(m_depth_detection_mutex);
//         for (int i = 0; i < 4; i++) {
//             current_coords[i] = m_depth_detection_coords[i];
//         }
//         current_depth_threshold = m_depth_threshold;
//         current_area_mask = m_depth_area_mask;
//     }
    
//     // 计算动态深度阈值
//     uint16_t background_depth = calculate_dynamic_depth_threshold(pFrame, current_coords);
//     uint16_t detection_threshold = background_depth - current_depth_threshold;
    
//     std::cout << "深度检测阈值: 背景深度 " << background_depth 
//               << " mm - 阈值偏移 " << current_depth_threshold 
//               << " mm = " << detection_threshold << " mm" << std::endl;
    
//     // 统计低于阈值的像素数量（物体通常比背景更近，深度值更小）
//     uint32_t detected_area = 0;
//     uint64_t depth_sum = 0;
//     uint16_t min_depth = 65535;
//     uint16_t max_depth = 0;
    
//     for (int i = 0; i < width; i++) {
//         for (int j = 0; j < height; j++) {
//             // 检查是否在有效检测区域内
//             if (is_position_valid_for_detection(i, j, current_coords)) {
                
//                 int index = j * width + i;
//                 uint16_t depth_value = depthImage[index];
                
//                 // 忽略无效深度值（0值）
//                 if (depth_value > 0) {
//                     // 如果深度值小于阈值，认为是物品像素
//                     // 物体通常比背景更近，所以深度值更小
//                     if (depth_value < detection_threshold) {
//                         detected_area++;
//                         depth_sum += depth_value;
                        
//                         // 更新最小和最大深度
//                         if (depth_value < min_depth) min_depth = depth_value;
//                         if (depth_value > max_depth) max_depth = depth_value;
//                     }
//                 }
//             }
//         }
//     }
    
//     std::cout << "检测到的物品像素数量: " << detected_area << std::endl;
    
//     // 计算平均深度
//     uint16_t avg_depth = (detected_area > 0) ? (depth_sum / detected_area) : 0;
    
//     // 填充检测结果
//     result.object_detected = (detected_area >= current_area_mask);
//     result.detected_area = detected_area;
//     result.background_depth = background_depth;
//     result.detection_threshold = detection_threshold;
//     result.frame_index = pFrame->getFrameIndex();
//     result.min_object_depth = (min_depth < 65535) ? min_depth : 0;
//     result.max_object_depth = max_depth;
//     result.avg_object_depth = avg_depth;
    
//     if (result.object_detected) {
//         std::cout << "=== 深度物品检测结果 ===" << std::endl;
//         std::cout << "检测结果: 发现物品" << std::endl;
//         std::cout << "检测面积: " << result.detected_area << " 像素" << std::endl;
//         std::cout << "物品深度范围: " << result.min_object_depth << " - " << result.max_object_depth << " mm" << std::endl;
//         std::cout << "物品平均深度: " << result.avg_object_depth << " mm" << std::endl;
//         std::cout << "背景深度: " << result.background_depth << " mm" << std::endl;
//         std::cout << "检测阈值: " << result.detection_threshold << " mm" << std::endl;
//     }
    
//     // 更新最新检测结果
//     {
//         std::lock_guard<std::mutex> lock(m_depth_detection_mutex);
//         m_latest_depth_result = result;
//     }
    
//     return result;
// }

// 添加帧数据到队列（自动生成时间戳）
void BerxelCameraTest::add_frame_to_queue(uint32_t frame_index, const std::vector<RGB888>& color_data,
                                       const std::vector<uint16_t>& depth_data, int width, int height) {
    std::lock_guard<std::mutex> lock(m_frame_queue_mutex);
    
    // 如果队列已满，移除最旧的帧
    while (m_frame_queue.size() >= m_max_queue_size) {
        m_frame_queue.pop();
        std::cout << "队列已满，移除最旧的帧" << std::endl;
    }
    
    // 添加新帧到队列（构造函数会自动设置时间戳）
    m_frame_queue.emplace(frame_index, color_data, depth_data, width, height);
    
    // 通知处理线程有新数据
    m_frame_condition.notify_one();
    
    std::cout << "帧 " << frame_index << " 已添加到队列，当前队列大小: " << m_frame_queue.size() << std::endl;
}

// 启动图像处理线程
void BerxelCameraTest::start_processing_thread() {
    std::cout << "启动图像处理线程..." << std::endl;
    
    // 设置线程运行标志
    m_processing_thread_running = true;
    
    // 创建并启动处理线程
    m_processing_thread = std::thread(&BerxelCameraTest::image_processing_thread, this);
    
    std::cout << "图像处理线程已启动" << std::endl;
}

// 停止图像处理线程
void BerxelCameraTest::stop_processing_thread() {
    std::cout << "停止图像处理线程..." << std::endl;
    
    // 设置线程退出标志
    m_processing_thread_running = false;
    
    // 通知处理线程退出
    m_frame_condition.notify_all();
    
    // 等待处理线程结束
    if (m_processing_thread.joinable()) {
        m_processing_thread.join();
    }
    
    std::cout << "图像处理线程已停止" << std::endl;
}

// 打印帧信息
void BerxelCameraTest::print_frame_info(const FrameData& frame_data) {
    std::cout << "=== 帧处理系统信息 ===" << std::endl;
    std::cout << "接收到新帧数据: " << frame_data.frame_index
              << " 尺寸: " << frame_data.width << "x" << frame_data.height
              << " 彩色数据大小: " << (frame_data.color_data.size() * sizeof(RGB888)) << " 字节"
              << " 深度数据大小: " << (frame_data.depth_data.size() * sizeof(uint16_t)) << " 字节" << std::endl;
    
    std::cout << "彩色帧数据已保存，帧索引: " << frame_data.frame_index << std::endl;
    std::cout << "深度帧数据已保存，帧索引: " << frame_data.frame_index << std::endl;
    std::cout << "帧数据已添加到处理队列，帧索引: " << frame_data.frame_index << std::endl;
}

// 图像处理线程函数
void BerxelCameraTest::image_processing_thread() {
    std::cout << "图像处理线程开始运行" << std::endl;
    
    while (m_processing_thread_running) {
        FrameData frame_data;
        bool has_frame = false;
        
        // 获取队列中的帧数据
        {
            std::unique_lock<std::mutex> lock(m_frame_queue_mutex);
            
            // 等待新帧或退出信号
            m_frame_condition.wait(lock, [this] {
                return !m_frame_queue.empty() || !m_processing_thread_running;
            });
            
            // 检查是否收到退出信号
            if (!m_processing_thread_running && m_frame_queue.empty()) {
                break;
            }
            
            
            // 获取队列中的第一帧
            if (!m_frame_queue.empty()) {
                frame_data = m_frame_queue.front();
                m_frame_queue.pop();
                has_frame = true;
                
                spdlog::info("从队列中取出帧 {}，剩余队列大小: {}", frame_data.frame_index, m_frame_queue.size());
            }
        }
        
        // 处理帧数据
        if (has_frame) {
            // 使用代理函数打印帧信息
            print_frame_info(frame_data);
            
            // 开始计时 - 执行图像处理任务
            auto start_time = std::chrono::high_resolution_clock::now();
            
            process_frame_data(frame_data);
            
            // 结束计时并计算处理时间
            auto end_time = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
            
            spdlog::info("完成处理帧 {}", frame_data.frame_index);
            spdlog::info("帧处理时间: {} 毫秒", duration.count());
            spdlog::info("=========================");
        }
    }
    
    std::cout << "图像处理线程结束运行" << std::endl;
}

// 处理单帧数据的函数
void BerxelCameraTest::process_frame_data(const FrameData& frame_data) {
    // 检查帧数据的有效性
    // 跳过前十帧数据不记录
    if (frame_data.frame_index < 10 ||
        frame_data.color_data.empty() || frame_data.depth_data.empty() ||
        frame_data.width <= 0 || frame_data.height <= 0) {
        std::cerr << "警告: 帧 " << frame_data.frame_index << " 数据无效，跳过处理" << std::endl;
        std::cerr << "  彩色数据大小: " << frame_data.color_data.size() << std::endl;
        std::cerr << "  深度数据大小: " << frame_data.depth_data.size() << std::endl;
        std::cerr << "  图像尺寸: " << frame_data.width << "x" << frame_data.height << std::endl;
        return;
    }

    // 更新最新帧缓存
    {
        std::lock_guard<std::mutex> lock(m_frame_mutex);
        
        // 保存最新的彩色和深度帧数据
        m_latest_color_frame = frame_data.color_data;
        m_latest_depth_frame = frame_data.depth_data;
        m_frame_width = frame_data.width;
        m_frame_height = frame_data.height;
        
        // 转换彩色图像为灰度图像
        size_t frameSize = frame_data.width * frame_data.height;
        m_latest_gray_frame.resize(frameSize);
        
        for (int i = 0; i < frame_data.width; i++) {
            for (int j = 0; j < frame_data.height; j++) {
                size_t index = static_cast<size_t>(j * frame_data.width + i);
                if (index < frame_data.color_data.size()) {
                    const RGB888& rgb = frame_data.color_data[index];
                    m_latest_gray_frame[index] = rgb_to_gray(rgb);
                } else {
                    // 处理越界情况
                    m_latest_gray_frame[index] = 0;
                    std::cerr << "警告: 彩色数据索引越界 " << index << " >= " << frame_data.color_data.size() << std::endl;
                }
            }
        }
        
        std::cout << "帧 " << frame_data.frame_index << " 灰度转换完成" << std::endl;
    }
    
    // // 执行灰度检测（默认启用）
    gray_detection_result result = detect_gray_object(frame_data);
    std::cout << "帧 " << frame_data.frame_index << " 灰度检测完成" << std::endl;
    
    // 更新灰度检测统计计数
    {
        std::lock_guard<std::mutex> lock(m_gray_stats_mutex);
        m_gray_detection_count++;
        if (result.object_detected) {
            m_gray_object_detected_count++;
        }
    }
    
    // 显示详细的灰度检测结果（类似深度检测的统计打印）
    spdlog::info("=== 灰度物品检测结果 ===");
    spdlog::info("检测结果: {}", (result.object_detected ? "发现物品" : "无物品"));
    spdlog::info("检测面积: {} 像素", result.detected_area);
    spdlog::info("背景灰度值: {}", (int)result.background_gray);
    spdlog::info("检测阈值: {}", (int)result.detection_threshold);
    spdlog::info("帧索引: {}", result.frame_index);
    
    // 显示灰度检测统计信息
    int gray_detection_count, gray_detected_count;
    {
        std::lock_guard<std::mutex> lock(m_gray_stats_mutex);
        gray_detection_count = m_gray_detection_count;
        gray_detected_count = m_gray_object_detected_count;
    }
    
    double success_rate = gray_detection_count > 0 ? (double)gray_detected_count / gray_detection_count * 100.0 : 0.0;
    spdlog::info("灰度检测统计: 总检测次数={}, 检测到物品次数={}, 检测成功率={:.2f}%",
                 gray_detection_count, gray_detected_count, success_rate);
    
    if (result.object_detected) {
        std::cout << ">>> 灰度物品检测成功！触发后续处理逻辑 <<<" << std::endl;
    }
    
    // 执行RGB检测
    detect_rgb_object_from_frame_data(frame_data);

    
    // // 在执行深度检测之前，先进行深度图像直方图均衡化处理
    // std::vector<uint16_t> equalized_depth_data;
    depth_detection_result depth_result;
    
    // if (!frame_data.depth_data.empty() && frame_data.width > 0 && frame_data.height > 0) {
    //     // 创建临时缓冲区存储均衡化后的深度数据
    //     equalized_depth_data.resize(frame_data.depth_data.size());
        
    //     // 应用直方图均衡化处理
    //     std::cout << "执行深度图像直方图均衡化处理..." << std::endl;
    //     histogram_equalization_depth(
    //         frame_data.depth_data.data(),
    //         equalized_depth_data.data(),
    //         frame_data.width,
    //         frame_data.height
    //     );
    //     std::cout << "深度图像直方图均衡化处理完成" << std::endl;
        
    //     // 创建包含均衡化深度数据的临时帧数据
    //     FrameData equalized_frame_data = frame_data;
    //     equalized_frame_data.depth_data = equalized_depth_data;
        
    //     // 执行深度检测（使用均衡化后的深度数据）
    //     depth_result = detect_depth_object_from_data(equalized_frame_data);
    // } else {
    //     // 如果深度数据无效，使用原始数据执行深度检测
    //     std::cerr << "警告: 深度数据无效，跳过直方图均衡化处理" << std::endl;
        depth_result = detect_depth_object_from_data(frame_data);
    // }
    
    // 更新统计计数
    {
        std::lock_guard<std::mutex> lock(m_depth_stats_mutex);
        m_depth_detection_count++;
        if (depth_result.object_detected) {
            m_depth_object_detected_count++;
        }
    }
        
        // 显示详细的深度检测结果（从custom_frame_callback移动过来的功能）
        spdlog::info("=== 深度物品检测结果 ===");
        spdlog::info("检测结果: {}", depth_result.object_detected ? "发现物品" : "无物品");
        spdlog::info("检测面积: {} 像素", depth_result.detected_area);
        if (depth_result.object_detected) {
            spdlog::info("物品深度范围: {} - {} mm", depth_result.min_object_depth, depth_result.max_object_depth);
            spdlog::info("物品平均深度: {} mm", depth_result.avg_object_depth);
        }
        spdlog::info("背景深度: {} mm", depth_result.background_depth);
        spdlog::info("检测阈值: {} mm", depth_result.detection_threshold);
        spdlog::info("帧索引: {}", depth_result.frame_index);
        
        // 显示统计信息
        int detection_count, detected_count;
        {
            std::lock_guard<std::mutex> lock(m_depth_stats_mutex);
            detection_count = m_depth_detection_count;
            detected_count = m_depth_object_detected_count;
        }
        
        double depth_success_rate = detection_count > 0 ? (double)detected_count / detection_count * 100.0 : 0.0;
        spdlog::info("检测统计: 总检测次数={}, 检测到物品次数={}, 检测成功率={:.2f}%",
                     detection_count, detected_count, depth_success_rate);

        // 使用frame_data中的timestamp，转换为毫秒时间戳字符串
        std::string timestamp = std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(
        frame_data.timestamp.time_since_epoch()).count());
        
        // 可以在这里添加检测结果的处理逻辑
        if (depth_result.object_detected) {
            std::cout << ">>> 深度物品检测成功！触发后续处理逻辑 <<<" << std::endl;

            // 2. 创建并保存标记了检测区域的深度图像
            std::vector<uint16_t> marked_depth_data = create_marked_depth_image(frame_data, depth_result);
            std::string marked_depth_filename = "depth_detected_marked_" + timestamp + ".bmp";
            save_depth_image_bmp(marked_depth_filename, marked_depth_data.data(),
                                frame_data.width, frame_data.height);
            std::cout << "深度检测成功，已保存标记深度图像: " << marked_depth_filename << std::endl;
        }
        
        // 1. 保存原始深度图像
        std::string original_depth_filename = "depth_detected_original_" + timestamp + ".bmp";
        save_depth_image_bmp(original_depth_filename, frame_data.depth_data.data(),
                            frame_data.width, frame_data.height);
        std::cout << "深度检测成功，已保存原始深度图像: " << original_depth_filename << std::endl;
        
        std::cout << "帧 " << frame_data.frame_index << " 深度检测完成" << std::endl;
}

// 从帧数据执行灰度检测
gray_detection_result BerxelCameraTest::detect_gray_object(const FrameData& frame_data) {
    gray_detection_result result;
    result.object_detected = false;
    result.detected_area = 0;
    
    // 获取当前检测参数
    point_pos_2d current_coords[4];
    uint8_t current_gray_mask;
    uint32_t current_area_mask;
    
    {
        std::lock_guard<std::mutex> lock(m_detection_mutex);
        for (int i = 0; i < 4; i++) {
            current_coords[i] = m_detection_coords[i];
        }
        current_gray_mask = m_gray_mask;
        current_area_mask = m_area_mask;
    }
    
    // 计算背景灰度值（使用动态计算函数）
    uint8_t background_gray = calculate_dynamic_gray_threshold(frame_data, current_coords);
    uint8_t final_threshold = background_gray + current_gray_mask;
    
    result.background_gray = background_gray;
    result.detection_threshold = final_threshold;
    
    // 统计超过阈值的像素数量
    uint32_t detected_area = 0;
    
    for (int y = 0; y < frame_data.height; y++) {
        for (int x = 0; x < frame_data.width; x++) {
            if (is_position_valid_for_detection(x, y, current_coords)) {
                int index = y * frame_data.width + x;
                const RGB888& rgb = frame_data.color_data[index];
                
                // 计算灰度值
                uint8_t gray_value = rgb_to_gray(rgb);
                
                // 如果灰度值超过阈值，认为是物品像素
                if (gray_value > final_threshold) {
                    detected_area++;
                }
            }
        }
    }
    
    // 填充检测结果
    result.detected_area = detected_area;
    result.object_detected = (detected_area >= current_area_mask);
    result.frame_index = frame_data.frame_index;
    
    // 更新最新检测结果
    if (result.object_detected) {
        std::lock_guard<std::mutex> lock(m_detection_mutex);
        m_latest_result = result;
    }
    
    return result;
}

// 从帧数据计算背景深度的辅助函数
uint16_t BerxelCameraTest::calculate_background_depth_from_data(const FrameData& frame_data) {
    if (frame_data.depth_data.empty() || frame_data.width == 0 || frame_data.height == 0) {
        return 0;
    }
    
    const std::vector<uint16_t>& depth_data = frame_data.depth_data;
    uint32_t background_sum = 0;
    uint32_t background_count = 0;
    
    // 计算有效区域外的背景深度
    for (int y = 0; y < frame_data.height; y++) {
        for (int x = 0; x < frame_data.width; x++) {
            // 检查是否在有效区域外（使用 m_depth_detection_coords 定义的区域）
            bool outside_valid_area = !is_position_valid_for_detection(x, y, m_depth_detection_coords);
            
            if (outside_valid_area) {
                uint16_t depth = depth_data[y * frame_data.width + x];
                if (depth > 0 && depth < 65535) {  // 有效深度值
                    background_sum += depth;
                    background_count++;
                    
                    // 打印采样点的深度数据（每100个点打印一次，避免输出过多）
                    if (background_count % 100 == 0) {
                        std::cout << "背景采样点 [" << x << "," << y << "] 深度值: "
                                  << depth << " mm" << std::endl;
                    }
                }
            }
        }
    }
    
    // 计算并打印最终的背景深度值
    uint16_t background_depth = background_count > 0 ? (background_sum / background_count) : 0;
    std::cout << "计算得到的背景深度值: " << background_depth
              << " mm (基于 " << background_count << " 个有效采样点)" << std::endl;
    
    return background_depth;
}

// 在有效区域内检测物品的辅助函数（使用连续性检测）
void BerxelCameraTest::detect_objects_in_valid_area(const FrameData& frame_data, depth_detection_result& result) {
    if (frame_data.depth_data.empty() || frame_data.width == 0 || frame_data.height == 0) {
        return;
    }
    
    const std::vector<uint16_t>& depth_data = frame_data.depth_data;
    uint32_t object_pixels = 0;
    uint32_t total_valid_pixels = 0;
    uint32_t depth_sum = 0;
    uint16_t min_depth = 65535;
    uint16_t max_depth = 0;
    
    // 保存超过阈值的像素点队列
    std::vector<point_pos_2d> threshold_pixels;
    
    // 使用深度检测区域坐标
    point_pos_2d valid_coords[4];
    for (int i = 0; i < 4; i++) {
        valid_coords[i] = m_depth_detection_coords[i];
    }
    
    // 第一遍扫描：收集超过阈值的像素点
    for (int y = 0; y < frame_data.height; y++) {
        for (int x = 0; x < frame_data.width; x++) {
            if (is_position_valid_for_detection(x, y, valid_coords)) {
                uint16_t depth = depth_data[y * frame_data.width + x];
                
                if (depth > 0 && depth < 65535) {  // 有效深度值
                    total_valid_pixels++;
                    depth_sum += depth;
                    
                    if (depth < min_depth) min_depth = depth;
                    if (depth > max_depth) max_depth = depth;
                    
                    // 原有的检测逻辑（暂时屏蔽）
                    /*
                    if (depth < (result.background_depth - m_depth_threshold)) {
                        object_pixels++;
                    }
                    */
                    
                    // 新的连续性检测：检查是否为物品像素（深度小于背景深度减去阈值）
                    if (depth < (result.background_depth - m_depth_threshold)) {
                        threshold_pixels.push_back({x, y});
                    }
                }
            }
        }
    }
    
    // 连续性检测：检查x和y方向的连续像素点
    bool has_continuous_object = false;
    object_pixels = 0;
    
    if (!threshold_pixels.empty()) {
        // 创建像素标记数组
        std::vector<std::vector<bool>> pixel_map(frame_data.height, std::vector<bool>(frame_data.width, false));
        for (const auto& pixel : threshold_pixels) {
            if (pixel.x >= 0 && pixel.x < frame_data.width && pixel.y >= 0 && pixel.y < frame_data.height) {
                pixel_map[pixel.y][pixel.x] = true;
            }
        }
        
        // ========== 屏蔽原有的x、y方向检测 ==========
        /*
        // 检查每一行的连续性
        int max_x_continuous = 0;
        for (int y = 0; y < frame_data.height; y++) {
            int current_continuous = 0;
            for (int x = 0; x < frame_data.width; x++) {
                if (pixel_map[y][x]) {
                    current_continuous++;
                    max_x_continuous = std::max(max_x_continuous, current_continuous);
                } else {
                    current_continuous = 0;
                }
            }
        }
        
        // 检查每一列的连续性
        int max_y_continuous = 0;
        for (int x = 0; x < frame_data.width; x++) {
            int current_continuous = 0;
            for (int y = 0; y < frame_data.height; y++) {
                if (pixel_map[y][x]) {
                    current_continuous++;
                    max_y_continuous = std::max(max_y_continuous, current_continuous);
                } else {
                    current_continuous = 0;
                }
            }
        }
        
        // 判断是否满足连续性要求
        // 新增限定条件：x、y连续点的数量必须有一个大于3倍的另一个方向
        bool basic_continuity = (max_x_continuous >= static_cast<int>(m_depth_area_mask) && max_y_continuous >= static_cast<int>(m_depth_area_mask));
        bool ratio_condition = (max_x_continuous > 3 * m_depth_area_mask) || (max_y_continuous > 3 * m_depth_area_mask);
        
        if (basic_continuity && ratio_condition) {
            has_continuous_object = true;
            object_pixels = threshold_pixels.size();
        }
        */
        
        // ========== 新的多方向连续性检测 ==========
        // 定义8个方向的向量：水平、垂直、对角线
        struct Direction {
            int dx, dy;
            std::string name;
        };
        
        std::vector<Direction> directions = {
            {1, 0, "水平右"},      // 0度
            {0, 1, "垂直下"},      // 90度
            {1, 1, "对角右下"},    // 45度
            {1, -1, "对角右上"},   // -45度(135度)
            {-1, 0, "水平左"},     // 180度
            {0, -1, "垂直上"},     // 270度
            {-1, -1, "对角左上"},  // 225度
            {-1, 1, "对角左下"}    // -135度(315度)
        };
        
        // 计算每个方向的最大连续长度
        std::vector<int> max_continuous_lengths(directions.size(), 0);
        
        for (size_t dir_idx = 0; dir_idx < directions.size(); dir_idx++) {
            const auto& dir = directions[dir_idx];
            
            // 对于每个起始点，沿着当前方向搜索连续像素
            for (int start_y = 0; start_y < frame_data.height; start_y++) {
                for (int start_x = 0; start_x < frame_data.width; start_x++) {
                    if (!pixel_map[start_y][start_x]) continue;
                    
                    int continuous_length = 0;
                    int x = start_x, y = start_y;
                    
                    // 沿着方向搜索连续像素
                    while (x >= 0 && x < frame_data.width && 
                           y >= 0 && y < frame_data.height && 
                           pixel_map[y][x]) {
                        continuous_length++;
                        x += dir.dx;
                        y += dir.dy;
                    }
                    
                    max_continuous_lengths[dir_idx] = std::max(
                        max_continuous_lengths[dir_idx], continuous_length);
                }
            }
        }
        
        // 寻找两个互相垂直的方向，且都满足连续性要求
        // 垂直关系：方向向量的点积为0，即 dx1*dx2 + dy1*dy2 = 0
        int best_dir1 = -1, best_dir2 = -1;
        int best_length1 = 0, best_length2 = 0;
        
        for (size_t i = 0; i < directions.size(); i++) {
            for (size_t j = i + 1; j < directions.size(); j++) {
                // 检查是否垂直
                int dot_product = directions[i].dx * directions[j].dx + 
                                 directions[i].dy * directions[j].dy;
                
                if (dot_product == 0) { // 垂直方向
                    int len1 = max_continuous_lengths[i];
                    int len2 = max_continuous_lengths[j];
                    
                    // 基本连续性要求：两个方向都要达到最小阈值
                    bool basic_continuity = (len1 >= static_cast<int>(m_depth_area_mask) && 
                                           len2 >= static_cast<int>(m_depth_area_mask));
                    
                    // 比例条件：至少有一个方向的连续长度是另一个的3倍以上
                    bool ratio_condition = (len1 > 7 * m_depth_area_mask) || 
                                          (len2 > 7 * m_depth_area_mask);
                    
                    if (basic_continuity && ratio_condition) {
                        // 选择连续长度更好的组合
                        int current_score = len1 + len2;
                        int best_score = best_length1 + best_length2;
                        
                        if (current_score > best_score) {
                            best_dir1 = i;
                            best_dir2 = j;
                            best_length1 = len1;
                            best_length2 = len2;
                        }
                    }
                }
            }
        }
        
        // 根据多方向检测结果判断连续性
        if (best_dir1 != -1 && best_dir2 != -1) {
            has_continuous_object = true;
            object_pixels = threshold_pixels.size();
            
            // 输出检测结果（调试信息）
            std::cout << "检测到垂直方向连续性: " 
                      << directions[best_dir1].name << "(" << best_length1 << ") 与 "
                      << directions[best_dir2].name << "(" << best_length2 << ")" << std::endl;
        }
    }
    
    // 更新检测结果
    result.detected_area = object_pixels;
    
    // 统计最大检测像素点数
    static int max_detected_depth_pixels = 0;
    if (object_pixels > max_detected_depth_pixels) {
        max_detected_depth_pixels = object_pixels;
    }
    
    // 保存有效像素数量和平均深度到正确的成员变量
    uint16_t avg_depth = total_valid_pixels > 0 ? (depth_sum / total_valid_pixels) : 0;
    result.min_object_depth = (min_depth != 65535) ? min_depth : 0;
    result.max_object_depth = max_depth;
    result.avg_object_depth = avg_depth;
    result.object_detected = has_continuous_object;
    
    // 打印深度检测面积信息用于调试
    spdlog::info("深度连续性检测统计: 检测到 {} 像素, 阈值要求 {} 像素, 历史最大 {} 像素, 检测结果: {}",
                 object_pixels,
                 m_depth_area_mask*m_depth_area_mask,
                 max_detected_depth_pixels,
                 (result.object_detected ? "发现物品" : "未发现物品"));
}

    // 创建标记了检测像素点的深度图像
    std::vector<uint16_t> BerxelCameraTest::create_marked_depth_image(const FrameData& frame_data, const depth_detection_result& result) {
    // 复制原始深度数据
    std::vector<uint16_t> marked_depth_data = frame_data.depth_data;
    
    if (frame_data.depth_data.empty() || frame_data.width == 0 || frame_data.height == 0) {
        return marked_depth_data;
    }
    
    // 使用深度检测区域坐标
    point_pos_2d valid_coords[4];
    for (int i = 0; i < 4; i++) {
        valid_coords[i] = m_depth_detection_coords[i];
    }
    
    // 遍历有效区域内的像素点，标记检测到的物品像素
    for (int y = 0; y < frame_data.height; y++) {
        for (int x = 0; x < frame_data.width; x++) {
            if (is_position_valid_for_detection(x, y, valid_coords)) {
                int index = y * frame_data.width + x;
                uint16_t depth = frame_data.depth_data[index];
                
                if (depth > 0 && depth < 65535) {  // 有效深度值
                    // 检查是否为物品像素（深度小于背景深度减去阈值）
                    if (depth < (result.background_depth - m_depth_threshold)) {
                        // 将检测到的物品像素点标记为高亮值（使用较大的深度值进行标记）
                        marked_depth_data[index] = 30000;  // 使用30000作为标记值，在深度图像中显示为明显的高亮
                    }
                    // 背景像素保持原来的深度值不变
                }
            }
        }
    }
    
    return marked_depth_data;
}

// 创建标记了检测像素点的彩色图像
std::vector<RGB888> BerxelCameraTest::create_marked_color_image(const FrameData& frame_data, const rgb_detection_result& result) {
    // 复制原始彩色数据
    std::vector<RGB888> marked_color_data = frame_data.color_data;
    
    if (frame_data.color_data.empty() || frame_data.width == 0 || frame_data.height == 0) {
        return marked_color_data;
    }
    
    // 使用RGB检测区域坐标
    point_pos_2d valid_coords[4];
    {
        std::lock_guard<std::mutex> lock(m_rgb_detection_mutex);
        for (int i = 0; i < 4; i++) {
            valid_coords[i] = m_detection_coords[i];
        }
    }
    
    // 获取检测参数
    uint8_t current_color_threshold;
    {
        std::lock_guard<std::mutex> lock(m_rgb_detection_mutex);
        current_color_threshold = m_rgb_color_threshold;
    }
    
    // 遍历有效区域内的像素点，标记检测到的物品像素
    for (int y = 0; y < frame_data.height; y++) {
        for (int x = 0; x < frame_data.width; x++) {
            if (is_position_valid_for_detection(x, y, valid_coords)) {
                int index = y * frame_data.width + x;
                RGB888 pixel_color = frame_data.color_data[index];
                
                // 使用与检测函数相同的逻辑判断是否为物品像素
                double r_diff = abs(pixel_color.r - result.background_color.r);
                double g_diff = abs(pixel_color.g - result.background_color.g);
                double b_diff = abs(pixel_color.b - result.background_color.b);
                
                // 如果任一通道差异超过阈值，认为是物品像素
                if (r_diff > current_color_threshold || g_diff > current_color_threshold || b_diff > current_color_threshold) {
                    // 将检测到的物品像素点标记为高亮颜色（红色高亮）
                    marked_color_data[index] = {255, 0, 0};  // 使用红色标记检测到的物品像素
                }
                // 背景像素保持原来的颜色不变
            }
        }
    }
    
    return marked_color_data;
}

// 从帧数据执行深度检测
depth_detection_result BerxelCameraTest::detect_depth_object_from_data(const FrameData& frame_data) {
    depth_detection_result result;
    result.object_detected = false;
    result.detected_area = 0;
    result.background_depth = 0;
    result.detection_threshold = m_depth_threshold;
    result.min_object_depth = 65535;
    result.max_object_depth = 0;
    result.avg_object_depth = 0;
    result.frame_index = frame_data.frame_index;
    
    // 获取深度检测区域坐标
    point_pos_2d depth_coords[4];
    {
        std::lock_guard<std::mutex> lock(m_depth_detection_mutex);
        for (int i = 0; i < 4; i++) {
            depth_coords[i] = m_depth_detection_coords[i];
        }
    }
    
    // 使用动态深度阈值函数计算背景深度
    result.background_depth = calculate_dynamic_depth_threshold(frame_data, depth_coords);
    
    // 使用辅助函数在有效区域内检测物品
    detect_objects_in_valid_area(frame_data, result);
    
    // 更新最新检测结果
    if (result.object_detected) {
        std::lock_guard<std::mutex> lock(m_depth_detection_mutex);
        m_latest_depth_result = result;
    }
    
    return result;
}

// ==================== RGB转HSV相关函数实现 ====================

// RGB转HSV
HSV BerxelCameraTest::rgb_to_hsv(const RGB888& rgb) {
    HSV hsv;
    
    // 将RGB值归一化到[0,1]范围
    float r = rgb.r / 255.0f;
    float g = rgb.g / 255.0f;
    float b = rgb.b / 255.0f;
    
    // 计算最大值和最小值
    float max_val = std::max(std::max(r, g), b);
    float min_val = std::min(std::min(r, g), b);
    float delta = max_val - min_val;
    
    // 计算HSV值
    
    // 计算V（明度）
    hsv.v = max_val;
    
    // 计算S（饱和度）
    if (max_val == 0.0f) {
        hsv.s = 0.0f;
    } else {
        hsv.s = delta / max_val;
    }
    
    // 计算H（色相）
    if (delta == 0.0f) {
        // 如果最大值和最小值相等，则色相未定义（灰色）
        hsv.h = 0.0f;
    } else {
        if (max_val == r) {
            // 红色区域
            hsv.h = 60.0f * fmodf((g - b) / delta, 6.0f);
        } else if (max_val == g) {
            // 绿色区域
            hsv.h = 60.0f * ((b - r) / delta + 2.0f);
        } else {
            // 蓝色区域
            hsv.h = 60.0f * ((r - g) / delta + 4.0f);
        }
    }
    
    // 确保H在[0,360)范围内
    if (hsv.h < 0.0f) {
        hsv.h += 360.0f;
    }
    
    return hsv;
}

// 将RGB图像转换为HSV图像
void BerxelCameraTest::convert_rgb_to_hsv(const RGB888* input_data, HSV* output_data, int width, int height) {
    if (!input_data || !output_data || width <= 0 || height <= 0) {
        std::cerr << "RGB转HSV: 无效的输入参数" << std::endl;
        return;
    }
    
    std::cout << "开始将RGB图像转换为HSV图像 (尺寸: " << width << "x" << height << ")..." << std::endl;
    
    // 逐像素转换
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            int index = y * width + x;
            output_data[index] = rgb_to_hsv(input_data[index]);
        }
    }
    
    std::cout << "RGB转HSV转换完成" << std::endl;
}

// 保存HSV图像为BMP文件
bool BerxelCameraTest::save_hsv_image_bmp(const std::string& filename, const HSV* hsv_data, int width, int height) {
    if (!hsv_data || width <= 0 || height <= 0) {
        std::cerr << "保存HSV图像: 无效的输入参数" << std::endl;
        return false;
    }
    
    std::cout << "开始保存HSV图像到BMP文件: " << filename << std::endl;
    
    // 创建RGB缓冲区用于保存（因为BMP不直接支持HSV格式）
    std::vector<RGB888> rgb_data(width * height);
    
    // 将HSV转换回RGB用于保存
    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            int index = y * width + x;
            const HSV& hsv = hsv_data[index];
            RGB888& rgb = rgb_data[index];
            
            // HSV转RGB算法
            float h = hsv.h;
            float s = hsv.s;
            float v = hsv.v;
            
            if (s <= 0.0f) {
                // 灰度
                rgb.r = rgb.g = rgb.b = static_cast<uint8_t>(v * 255);
            } else {
                h = fmodf(h, 360.0f) / 60.0f;  // 将H转换到[0,6)范围
                int hi = static_cast<int>(h);
                float f = h - hi;
                float p = v * (1.0f - s);
                float q = v * (1.0f - s * f);
                float t = v * (1.0f - s * (1.0f - f));
                
                float r, g, b;
                
                switch (hi) {
                    case 0: r = v; g = t; b = p; break;
                    case 1: r = q; g = v; b = p; break;
                    case 2: r = p; g = v; b = t; break;
                    case 3: r = p; g = q; b = v; break;
                    case 4: r = t; g = p; b = v; break;
                    default: r = v; g = p; b = q; break;
                }
                
                rgb.r = static_cast<uint8_t>(r * 255);
                rgb.g = static_cast<uint8_t>(g * 255);
                rgb.b = static_cast<uint8_t>(b * 255);
            }
        }
    }
    
    // 使用现有的彩色图像保存函数
    bool result = save_color_image_bmp(filename, rgb_data.data(), width, height);
    
    if (result) {
        std::cout << "HSV图像已成功保存为BMP文件: " << filename << std::endl;
    } else {
        std::cerr << "保存HSV图像失败" << std::endl;
    }
    
    return result;
}

// ==================== RGB物品检测方法实现 ====================

// 计算动态RGB颜色阈值
RGB888 BerxelCameraTest::calculate_dynamic_rgb_threshold(const RGB888* color_data, int width, int height) {
    if (!color_data || width <= 0 || height <= 0) {
        std::cout << "计算RGB阈值: 无效的输入参数，使用默认阈值" << std::endl;
        return {128, 128, 128}; // 默认阈值
    }

    // 获取校准点坐标
    point_pos_2d calib_points[4];
    {
        std::lock_guard<std::mutex> lock(m_rgb_detection_mutex);
        for (int i = 0; i < 4; i++) {
            calib_points[i] = m_detection_coords[i];
        }
    }

    int total_r = 0, total_g = 0, total_b = 0;
    int valid_points = 0;

    for (int i = 0; i < 4; i++) {
        int x = calib_points[i].x;
        int y = calib_points[i].y;
        
        if (x >= 0 && x < width && y >= 0 && y < height) {
            int index = y * width + x;
            RGB888 rgb_value = color_data[index];
            
            if (rgb_value.r > 0 || rgb_value.g > 0 || rgb_value.b > 0) { // 有效RGB值
                total_r += rgb_value.r;
                total_g += rgb_value.g;
                total_b += rgb_value.b;
                valid_points++;
                
                // 计算HSV值
                HSV hsv_value = rgb_to_hsv(rgb_value);
                
                std::cout << "校准点 " << i << " (" << x << ", " << y
                         << ") RGB值: (" << (int)rgb_value.r << ", "
                         << (int)rgb_value.g << ", " << (int)rgb_value.b << ")"
                         << " HSV值: (" << hsv_value.h << ", "
                         << hsv_value.s << ", " << hsv_value.v << ")" << std::endl;
            }
        } else {
            std::cout << "校准点 " << i << " (" << x << ", " << y 
                     << ") 索引越界，跳过该点" << std::endl;
        }
    }

    if (valid_points == 0) {
        std::cout << "没有有效的校准点,使用默认RGB阈值" << std::endl;
        return {128, 128, 128}; // 如果没有有效点，返回默认值
    }

    RGB888 avg_rgb;
    avg_rgb.r = static_cast<uint8_t>(total_r / valid_points);
    avg_rgb.g = static_cast<uint8_t>(total_g / valid_points);
    avg_rgb.b = static_cast<uint8_t>(total_b / valid_points);
    
    // 计算平均RGB值对应的HSV值
    HSV avg_hsv = rgb_to_hsv(avg_rgb);
    
    std::cout << "计算得到的平均背景RGB值: (" << (int)avg_rgb.r << ", "
             << (int)avg_rgb.g << ", " << (int)avg_rgb.b << ")"
             << " 对应HSV值: (" << avg_hsv.h << ", "
             << avg_hsv.s << ", " << avg_hsv.v << ")" << std::endl;

    return avg_rgb;
}

// 设置RGB检测参数
void BerxelCameraTest::set_rgb_detection_params(const point_pos_2d coords[4], uint8_t color_threshold, int area_mask) {
    std::lock_guard<std::mutex> lock(m_rgb_detection_mutex);
    
    std::cout << "设置RGB检测参数:" << std::endl;
    std::cout << "  检测区域坐标:" << std::endl;
    for (int i = 0; i < 4; i++) {
        m_detection_coords[i] = coords[i];
        std::cout << "    点" << i << ": (" << coords[i].x << ", " << coords[i].y << ")" << std::endl;
    }
    
    m_rgb_color_threshold = color_threshold;
    m_rgb_area_mask = area_mask;
    
    std::cout << "  颜色阈值: " << (int)m_rgb_color_threshold << std::endl;
    std::cout << "  最小检测面积: " << m_rgb_area_mask << " 像素" << std::endl;
}

// 计算RGB颜色差异
double BerxelCameraTest::calculate_rgb_color_difference(const RGB888& color1, const RGB888& color2) {
    // 使用欧几里得距离计算RGB颜色差异
    double dr = static_cast<double>(color1.r) - static_cast<double>(color2.r);
    double dg = static_cast<double>(color1.g) - static_cast<double>(color2.g);
    double db = static_cast<double>(color1.b) - static_cast<double>(color2.b);
    
    return std::sqrt(dr * dr + dg * dg + db * db);
}

// 从帧数据执行RGB检测
void BerxelCameraTest::detect_rgb_object_from_frame_data(const FrameData& frame_data) {
    if (frame_data.color_data.empty() || frame_data.width <= 0 || frame_data.height <= 0) {
        std::cout << "RGB检测: 无效的帧数据" << std::endl;
        return;
    }

    // // 在执行RGB检测之前进行高斯模糊处理
    // std::cout << "RGB检测: 开始高斯模糊预处理..." << std::endl;
    
    // // 创建模糊后的图像数据缓冲区
    // std::vector<RGB888> blurred_color_data(frame_data.color_data.size());
    
    // // 应用轻度高斯模糊 (sigma=1.0, kernel_size=5)
    // gaussian_blur_color(frame_data.color_data.data(),blurred_color_data.data(),frame_data.width,frame_data.height,
    //     1,  // sigma值，轻度模糊
    //     5     // 核大小
    // );
    
    // std::cout << "RGB检测: 高斯模糊预处理完成" << std::endl;

    // 使用模糊后的图像数据执行RGB检测
    rgb_detection_result rgb_result = detect_rgb_object_from_data(
        frame_data.color_data.data(),  // 使用模糊后的数据
        frame_data.width,
        frame_data.height
    );

    // 更新统计信息
    {
        std::lock_guard<std::mutex> lock(m_rgb_stats_mutex);
        m_rgb_detection_count++;
        if (rgb_result.object_detected) {
            m_rgb_object_detected_count++;
        }
    }

    // 输出检测结果
    if (rgb_result.object_detected) {
        spdlog::info("RGB检测: 发现物品!");
        spdlog::info("  检测面积: {} 像素", rgb_result.detected_area);
        spdlog::info("  背景颜色: RGB({}, {}, {})",
                    (int)rgb_result.background_color.r, (int)rgb_result.background_color.g, (int)rgb_result.background_color.b);
        spdlog::info("  平均物体颜色: RGB({}, {}, {})",
            (int)rgb_result.avg_object_color.r, (int)rgb_result.avg_object_color.g, (int)rgb_result.avg_object_color.b);
        spdlog::info("  主导色相: {}°", rgb_result.dominant_hue);
        spdlog::info("  颜色方差: {}", rgb_result.color_variance);
    } else {
        spdlog::info("RGB检测: 未发现物品");
        spdlog::info("  背景颜色: RGB({}, {}, {})",
                    (int)rgb_result.background_color.r, (int)rgb_result.background_color.g, (int)rgb_result.background_color.b);
    }

    // 输出统计信息
    int total_detections, detected_count;
    {
        std::lock_guard<std::mutex> lock(m_rgb_stats_mutex);
        total_detections = m_rgb_detection_count;
        detected_count = m_rgb_object_detected_count;
    }


    // 使用frame_data中的timestamp，转换为毫秒时间戳字符串
    std::string timestamp = std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(
        frame_data.timestamp.time_since_epoch()).count());

    if (total_detections > 0) {
        double detection_rate = (double)detected_count / total_detections * 100.0;
        spdlog::info("RGB检测统计: {}/{} ({:.1f}%)", detected_count, total_detections, detection_rate);
        
        if (rgb_result.object_detected) {    
                // 2. 保存标记的彩色图像
                std::vector<RGB888> marked_color_data = create_marked_color_image(frame_data, rgb_result);
                std::string marked_color_filename = "color_detected_marked_" + timestamp + ".bmp";
                save_color_image_bmp(marked_color_filename, marked_color_data.data(),
                                   frame_data.width, frame_data.height);
                std::cout << "彩色检测成功，已保存标记彩色图像: " << marked_color_filename << std::endl;
            }
        } 
        // 检测不到物品时，只保存彩色图像
        std::string color_filename = "color_no_detection_" + timestamp + ".bmp";
        save_color_image_bmp(color_filename, frame_data.color_data.data(),
                            frame_data.width, frame_data.height);
        std::cout << "未检测到物品，已保存彩色图像: " << color_filename << std::endl;

    std::cout << "帧 " << frame_data.frame_index << " RGB检测完成" << std::endl;
}

// 主要的RGB物品检测函数
rgb_detection_result BerxelCameraTest::detect_rgb_object_from_data(const RGB888* color_data, int width, int height) {
    rgb_detection_result result;
    
    // 初始化检测结果
    result.object_detected = false;
    result.detected_area = 0;
    result.background_color = {0, 0, 0};
    result.avg_object_color = {0, 0, 0};
    result.dominant_hue = 0.0f;
    result.color_variance = 0.0;

    if (!color_data || width <= 0 || height <= 0) {
        std::cout << "RGB检测: 无效的输入参数" << std::endl;
        return result;
    }

    // 获取当前检测参数
    point_pos_2d current_coords[4];
    uint8_t current_color_threshold;
    int current_area_mask;
    {
        std::lock_guard<std::mutex> lock(m_rgb_detection_mutex);
        for (int i = 0; i < 4; i++) {
            current_coords[i] = m_detection_coords[i];
        }
        current_color_threshold = m_rgb_color_threshold;
        current_area_mask = m_rgb_area_mask;
    }

    // 计算背景RGB颜色
    RGB888 background_rgb = calculate_dynamic_rgb_threshold(color_data, width, height);
    result.background_color = background_rgb;

    // // 确定检测区域边界
    // int min_x = std::min(std::min(current_coords[0].x, current_coords[1].x),
    //                      std::min(current_coords[2].x, current_coords[3].x));
    // int max_x = std::max(std::max(current_coords[0].x, current_coords[1].x),
    //                      std::max(current_coords[2].x, current_coords[3].x));
    // int min_y = std::min(std::min(current_coords[0].y, current_coords[1].y),
    //                      std::min(current_coords[2].y, current_coords[3].y));
    // int max_y = std::max(std::max(current_coords[0].y, current_coords[1].y),
    //                      std::max(current_coords[2].y, current_coords[3].y));

    // 在检测区域内进行物品检测
    int detected_area = 0;
    long long total_r = 0, total_g = 0, total_b = 0;
    std::vector<float> hue_values;
    std::vector<double> color_differences;
    
    // 新增：保存超过阈值的像素点队列
    std::vector<point_pos_2d> threshold_pixels;

    for (int y = 0; y < height; y++) {
        for (int x = 0; x < width; x++) {
            if (is_position_valid_for_detection(x, y, current_coords)) {
                int index = y * width + x;
                if (index >= 0 && index < width * height) {
                    RGB888 pixel_color = color_data[index];
                    
                    // 原来的检测逻辑（已屏蔽）
                    // double color_diff = calculate_rgb_color_difference(pixel_color, background_rgb);
                    // if (color_diff > current_color_threshold) {
                    
                    // 原有的检测逻辑（暂时屏蔽）
                    /*
                    double r_diff = abs(pixel_color.r - background_rgb.r);
                    double g_diff = abs(pixel_color.g - background_rgb.g);
                    double b_diff = abs(pixel_color.b - background_rgb.b);
                    
                    // 任何一个通道超过阈值都被记录
                    if (r_diff > current_color_threshold ||
                        g_diff > current_color_threshold ||
                        b_diff > current_color_threshold) {
                        detected_area++;
                        total_r += pixel_color.r;
                        total_g += pixel_color.g;
                        total_b += pixel_color.b;
                        
                        // 计算色相值用于主导色相分析
                        HSV hsv = rgb_to_hsv(pixel_color);
                        hue_values.push_back(hsv.h);
                        // color_differences相关功能暂时屏蔽
                        // color_differences.push_back(color_diff);
                    }
                    */
                    
                    // 新的连续性检测算法
                    double r_diff = abs(pixel_color.r - background_rgb.r);
                    double g_diff = abs(pixel_color.g - background_rgb.g);
                    double b_diff = abs(pixel_color.b - background_rgb.b);
                    
                    // 检查是否有任何通道超过阈值
                    if (r_diff > current_color_threshold ||
                        g_diff > current_color_threshold ||
                        b_diff > current_color_threshold) {
                        // 将超过阈值的像素点保存到队列中
                        threshold_pixels.push_back({x, y});
                    }
                }
            }
        }
    }

    // 简化的连续性检测：检查x和y方向的连续像素点
    bool has_continuous_object = false;
    detected_area = 0;
    
    if (!threshold_pixels.empty()) {
        // 创建像素标记数组
        std::vector<std::vector<bool>> pixel_map(height, std::vector<bool>(width, false));
        for (const auto& pixel : threshold_pixels) {
            if (pixel.x >= 0 && pixel.x < width && pixel.y >= 0 && pixel.y < height) {
                pixel_map[pixel.y][pixel.x] = true;
            }
        }
        
        // 检查每一行的连续性
        int max_x_continuous = 0;
        for (int y = 0; y < height; y++) {
            int current_continuous = 0;
            for (int x = 0; x < width; x++) {
                if (pixel_map[y][x]) {
                    current_continuous++;
                    max_x_continuous = std::max(max_x_continuous, current_continuous);
                } else {
                    current_continuous = 0;
                }
            }
        }
        
        // 检查每一列的连续性
        int max_y_continuous = 0;
        for (int x = 0; x < width; x++) {
            int current_continuous = 0;
            for (int y = 0; y < height; y++) {
                if (pixel_map[y][x]) {
                    current_continuous++;
                    max_y_continuous = std::max(max_y_continuous, current_continuous);
                } else {
                    current_continuous = 0;
                }
            }
        }
        
        // 判断是否满足连续性要求
        if (max_x_continuous >= current_area_mask && max_y_continuous >= current_area_mask) {
            has_continuous_object = true;
            detected_area = threshold_pixels.size();
            
            // 计算颜色信息
            for (const auto& pixel : threshold_pixels) {
                int index = pixel.y * width + pixel.x;
                if (index >= 0 && index < width * height) {
                    RGB888 pixel_color = color_data[index];
                    total_r += pixel_color.r;
                    total_g += pixel_color.g;
                    total_b += pixel_color.b;
                    
                    HSV hsv = rgb_to_hsv(pixel_color);
                    hue_values.push_back(hsv.h);
                }
            }
        }
    }

    // 更新检测结果
    result.detected_area = detected_area;
    result.object_detected = has_continuous_object;
    
    // 统计最大检测像素点数
    static int max_detected_pixels = 0;
    if (detected_area > max_detected_pixels) {
        max_detected_pixels = detected_area;
    }
    
    // 打印检测面积信息用于调试
    spdlog::info("RGB检测面积统计: 检测到 {} 像素, 阈值要求 {} 像素, 历史最大 {} 像素, 检测结果: {}",
                 detected_area,
                 current_area_mask*current_area_mask,
                 max_detected_pixels,
                 (result.object_detected ? "发现物品" : "未发现物品"));

    if (result.object_detected && detected_area > 0)
    {
        // 计算平均物体颜色
        result.avg_object_color.r = static_cast<uint8_t>(total_r / detected_area);
        result.avg_object_color.g = static_cast<uint8_t>(total_g / detected_area);
        result.avg_object_color.b = static_cast<uint8_t>(total_b / detected_area);

        // 计算主导色相（最常见的色相值）
        if (!hue_values.empty()) {
            std::sort(hue_values.begin(), hue_values.end());
            result.dominant_hue = hue_values[hue_values.size() / 2]; // 中位数作为主导色相
        }

        // 计算颜色方差
        if (!color_differences.empty()) {
            double mean_diff = 0.0;
            for (double diff : color_differences) {
                mean_diff += diff;
            }
            mean_diff /= color_differences.size();

            double variance = 0.0;
            for (double diff : color_differences) {
                variance += (diff - mean_diff) * (diff - mean_diff);
            }
            result.color_variance = variance / color_differences.size();
        }
    }

    // 更新最新检测结果
    {
        std::lock_guard<std::mutex> lock(m_rgb_detection_mutex);
        m_latest_rgb_result = result;
    }

    return result;
}
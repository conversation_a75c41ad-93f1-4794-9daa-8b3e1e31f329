/**
 * @file example.cpp
 * @brief compete_manage防碰撞管理器测试程序
 * @details 演示compete_manage类的主要功能，包括位置更新和最近位置获取等，支持多线程车辆移动模拟
 * <AUTHOR>
 * @date 2025-01-05
 * @version v1.1.0
 */

#include "compete_manage.hpp"
#include <iostream>
#include <thread>
#include <atomic>
#include <chrono>
#include <vector>
#include <mutex>

// 全局变量用于控制线程
std::atomic<bool> g_stop_simulation{false};
std::mutex g_output_mutex;

/**
 * @brief 车辆移动状态枚举
 */
enum class VehicleState {
    IDLE,           // 空闲状态
    MOVING,         // 移动中
    ARRIVED,        // 已到达
    STOPPED         // 已停止
};

/**
 * @brief 车辆移动模拟器类
 */
class VehicleSimulator {
public:
    VehicleSimulator(int device_id, double speed = 50.0, int update_interval = 100)
        : m_device_id(device_id)
        , m_speed(speed)
        , m_update_interval(update_interval)
        , m_current_position(0)
        , m_target_position(0)
        , m_state(VehicleState::IDLE)
        , m_stop_requested(false)
        , m_manager(&compete_manage::get_instance())
    {
    }

    ~VehicleSimulator() {
        stop_movement();
    }

    bool start_move_to_position(int target_position) {
        if (m_state == VehicleState::MOVING) {
            return false; // 已经在移动中
        }

        // 获取当前位置
        int current_pos;
        if (!m_manager->compete_manage_get_current_position(m_device_id, &current_pos)) {
            std::lock_guard<std::mutex> lock(g_output_mutex);
            std::cout << "设备" << m_device_id << "获取当前位置失败" << std::endl;
            return false;
        }

        m_current_position = current_pos;
        m_target_position = target_position;
        m_state = VehicleState::MOVING;
        m_stop_requested = false;

        // 启动移动线程
        m_movement_thread = std::thread(&VehicleSimulator::movement_thread, this);

        {
            std::lock_guard<std::mutex> lock(g_output_mutex);
            std::cout << "设备" << m_device_id << "开始移动: " << current_pos 
                      << " -> " << target_position << std::endl;
        }
        return true;
    }

    void stop_movement() {
        m_stop_requested = true;
        if (m_movement_thread.joinable()) {
            m_movement_thread.join();
        }
        m_state = VehicleState::STOPPED;
    }

    VehicleState get_state() const { return m_state; }
    int get_current_position() const { return m_current_position; }
    int get_target_position() const { return m_target_position; }
    int get_device_id() const { return m_device_id; }

    bool wait_for_completion(int timeout_ms = 0) {
        if (m_movement_thread.joinable()) {
            if (timeout_ms > 0) {
                auto start = std::chrono::steady_clock::now();
                while (m_state == VehicleState::MOVING) {
                    auto now = std::chrono::steady_clock::now();
                    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - start);
                    if (elapsed.count() >= timeout_ms) {
                        return false;
                    }
                    std::this_thread::sleep_for(std::chrono::milliseconds(10));
                }
            } else {
                m_movement_thread.join();
            }
        }
        return m_state == VehicleState::ARRIVED;
    }

private:
    void movement_thread() {
        int start_pos = m_current_position;
        int target_pos = m_target_position;
        double speed = m_speed;
        int update_interval = m_update_interval;

        if (start_pos == target_pos) {
            m_state = VehicleState::ARRIVED;
            return;
        }

        int direction = (target_pos > start_pos) ? 1 : -1;
        int distance = std::abs(target_pos - start_pos);
        double time_to_complete = distance / speed; // 秒
        int total_updates = static_cast<int>(time_to_complete * 1000 / update_interval);

        if (total_updates == 0) total_updates = 1;

        {
            std::lock_guard<std::mutex> lock(g_output_mutex);
            std::cout << "设备" << m_device_id << "移动参数: 距离=" << distance 
                      << "mm, 预计时间=" << time_to_complete << "s, 更新次数=" << total_updates << std::endl;
        }

        for (int i = 0; i <= total_updates && !m_stop_requested && !g_stop_simulation; ++i) {
            // 计算当前位置
            double progress = static_cast<double>(i) / total_updates;
            int new_position = start_pos + static_cast<int>(progress * distance * direction);
            
            m_current_position = new_position;
            
            // 更新到compete_manage
            m_manager->compete_manage_update_current_position(m_device_id, new_position);

            {
                std::lock_guard<std::mutex> lock(g_output_mutex);
                std::cout << "设备" << m_device_id << "位置更新: " << new_position 
                          << " (进度: " << static_cast<int>(progress * 100) << "%)" << std::endl;
            }

            if (i < total_updates) {
                std::this_thread::sleep_for(std::chrono::milliseconds(update_interval));
            }
        }

        if (!m_stop_requested && !g_stop_simulation) {
            m_current_position = target_pos;
            m_manager->compete_manage_update_current_position(m_device_id, target_pos);
            m_state = VehicleState::ARRIVED;
            
            std::lock_guard<std::mutex> lock(g_output_mutex);
            std::cout << "设备" << m_device_id << "已到达目标位置: " << target_pos << std::endl;
        } else {
            m_state = VehicleState::STOPPED;
            
            std::lock_guard<std::mutex> lock(g_output_mutex);
            std::cout << "设备" << m_device_id << "移动被停止，当前位置: " << m_current_position.load() << std::endl;
        }
    }

    int m_device_id;
    std::atomic<double> m_speed;
    std::atomic<int> m_update_interval;
    std::atomic<int> m_current_position;
    std::atomic<int> m_target_position;
    std::atomic<VehicleState> m_state;
    std::atomic<bool> m_stop_requested;
    std::thread m_movement_thread;
    compete_manage* m_manager;
};

/**
 * @brief 打印测试标题
 */
void print_test_title(const std::string& title) {
    std::cout << "\n" << std::string(50, '=') << std::endl;
    std::cout << "测试: " << title << std::endl;
    std::cout << std::string(50, '=') << std::endl;
}

/**
 * @brief 打印测试结果
 */
void print_test_result(const std::string& test_name, bool success) {
    std::cout << "[" << test_name << "] ";
    if (success) {
        std::cout << "✓ 成功" << std::endl;
    } else {
        std::cout << "✗ 失败" << std::endl;
    }
}

/**
 * @brief 测试当前位置管理功能
 */
void test_current_position_management() {
    print_test_title("当前位置管理功能测试");
    
    try {
        compete_manage& manager = compete_manage::get_instance();
        
        // 测试更新设备位置
        std::cout << "测试更新设备位置..." << std::endl;
        manager.compete_manage_update_current_position(1, 100);
        manager.compete_manage_update_current_position(2, 200);
        manager.compete_manage_update_current_position(3, 300);
        std::cout << "设备位置更新完成" << std::endl;
        
        // 测试获取设备位置
        std::cout << "测试获取设备位置..." << std::endl;
        int current_pos;
        
        bool result1 = manager.compete_manage_get_current_position(1, &current_pos);
        std::cout << "设备1位置获取结果: " << (result1 ? "成功" : "失败") 
                  << ", 位置: " << current_pos << std::endl;
        
        bool result2 = manager.compete_manage_get_current_position(2, &current_pos);
        std::cout << "设备2位置获取结果: " << (result2 ? "成功" : "失败") 
                  << ", 位置: " << current_pos << std::endl;
        
        bool result3 = manager.compete_manage_get_current_position(3, &current_pos);
        std::cout << "设备3位置获取结果: " << (result3 ? "成功" : "失败") 
                  << ", 位置: " << current_pos << std::endl;
        
        // 测试获取不存在的设备位置
        bool result4 = manager.compete_manage_get_current_position(999, &current_pos);
        std::cout << "不存在设备999位置获取结果: " << (result4 ? "成功" : "失败") << std::endl;
        
        print_test_result("当前位置管理功能测试", result1 && result2 && result3 && !result4);
        
    } catch (const std::exception& e) {
        std::cout << "当前位置管理测试异常: " << e.what() << std::endl;
        print_test_result("当前位置管理功能测试", false);
    }
}

/**
 * @brief 测试最近位置获取功能
 */
void test_nearest_position() {
    print_test_title("最近位置获取功能测试");
    
    try {
        compete_manage& manager = compete_manage::get_instance();
        
        // 获取第一个测试函数设置的当前位置
        int current_pos_dev1, current_pos_dev2, current_pos_dev3;
        bool pos_valid_dev1 = manager.compete_manage_get_current_position(1, &current_pos_dev1);
        bool pos_valid_dev2 = manager.compete_manage_get_current_position(2, &current_pos_dev2);
        bool pos_valid_dev3 = manager.compete_manage_get_current_position(3, &current_pos_dev3);
        
        if (!pos_valid_dev1 || !pos_valid_dev2 || !pos_valid_dev3) {
            std::cout << "警告: 无法获取设备当前位置，请先运行 test_current_position_management" << std::endl;
            return;
        }
        
        std::cout << "使用当前设备位置进行测试:" << std::endl;
        std::cout << "设备1当前位置: " << current_pos_dev1 << std::endl;
        std::cout << "设备2当前位置: " << current_pos_dev2 << std::endl;
        std::cout << "设备3当前位置: " << current_pos_dev3 << std::endl;
        
        int nearest_pos;
        bool can_reach;
        
        // 基于设备1当前位置(100)测试到目标位置300
        std::cout << "测试设备1从位置" << current_pos_dev1 << "到目标位置300..." << std::endl;
        can_reach = manager.compete_manage_x_track_get_nearest_position(1, 300, &nearest_pos, false);
        std::cout << "设备1测试结果: " << (can_reach ? "可以到达目标" : "只能到达中间位置") 
                  << ", 最近位置: " << nearest_pos << std::endl;
        
        // 基于设备1当前位置(100)测试到目标位置500
        std::cout << "测试设备1从位置" << current_pos_dev1 << "到目标位置500..." << std::endl;
        can_reach = manager.compete_manage_x_track_get_nearest_position(1, 500, &nearest_pos, false);
        std::cout << "设备1测试结果: " << (can_reach ? "可以到达目标" : "只能到达中间位置") 
                  << ", 最近位置: " << nearest_pos << std::endl;
        
        // 基于设备2当前位置(200)测试到目标位置150
        std::cout << "测试设备2从位置" << current_pos_dev2 << "到目标位置150..." << std::endl;
        can_reach = manager.compete_manage_x_track_get_nearest_position(2, 150, &nearest_pos, false);
        std::cout << "设备2测试结果: " << (can_reach ? "可以到达目标" : "只能到达中间位置") 
                  << ", 最近位置: " << nearest_pos << std::endl;
        
        // 测试设备1竞争格口模式
        std::cout << "测试设备1竞争格口模式到目标位置400..." << std::endl;
        can_reach = manager.compete_manage_x_track_get_nearest_position(1, 400, &nearest_pos, true);
        std::cout << "设备1竞争格口测试结果: " << (can_reach ? "可以到达目标" : "只能到达中间位置") 
                  << ", 最近位置: " << nearest_pos << std::endl;
        
        // 测试相同位置(设备1当前位置)
        std::cout << "测试相同位置(设备1当前位置" << current_pos_dev1 << ")..." << std::endl;
        can_reach = manager.compete_manage_x_track_get_nearest_position(1, current_pos_dev1, &nearest_pos, false);
        std::cout << "相同位置测试结果: " << (can_reach ? "可以到达目标" : "只能到达中间位置") 
                  << ", 最近位置: " << nearest_pos << std::endl;
        
        print_test_result("最近位置获取功能测试", true);
        
    } catch (const std::exception& e) {
        std::cout << "最近位置获取测试异常: " << e.what() << std::endl;
        print_test_result("最近位置获取功能测试", false);
    }
}

/**
 * @brief 测试单个车辆移动模拟
 */
void test_single_vehicle_movement() {
    print_test_title("单个车辆移动模拟测试");
    
    try {
        compete_manage& manager = compete_manage::get_instance();
        
        // 使用已注册的设备1进行测试
        int device_id = 1;
        int target_pos = 400;
        
        // 获取设备1的当前位置
        int start_pos;
        if (!manager.compete_manage_get_current_position(device_id, &start_pos)) {
            std::cout << "无法获取设备" << device_id << "的当前位置！" << std::endl;
            print_test_result("单个车辆移动模拟测试", false);
            return;
        }
        
        std::cout << "使用已注册设备" << device_id << "，当前位置: " << start_pos << std::endl;
        
        // 获取最近可达位置
        int nearest_pos;
        bool can_reach = manager.compete_manage_x_track_get_nearest_position(device_id, target_pos, &nearest_pos, false);
        std::cout << "设备" << device_id << "到目标位置" << target_pos 
                  << (can_reach ? "可以到达" : "只能到达最近位置") << ": " << nearest_pos << std::endl;
        
        // 创建车辆模拟器并开始移动
        VehicleSimulator simulator(device_id, 100.0, 200); // 速度100mm/s，更新间隔200ms
        
        if (simulator.start_move_to_position(nearest_pos)) {
            std::cout << "车辆开始移动到位置: " << nearest_pos << std::endl;
            
            // 等待移动完成
            bool completed = simulator.wait_for_completion(10000); // 10秒超时
            
            if (completed) {
                std::cout << "车辆移动完成！" << std::endl;
                
                // 验证最终位置
                int final_pos;
                if (manager.compete_manage_get_current_position(device_id, &final_pos)) {
                    std::cout << "最终位置: " << final_pos << std::endl;
                    
                    // 车辆移动完成后，测试2号设备可到达的最近位置
                    std::cout << "\n=== 测试2号设备可到达的最近位置 ===" << std::endl;
                    int device_2 = 2;
                    int test_target = 600; // 测试目标位置
                    int nearest_pos_2;
                    bool can_reach_2 = manager.compete_manage_x_track_get_nearest_position(device_2, test_target, &nearest_pos_2, false);
                    
                    std::cout << "2号设备到目标位置" << test_target 
                              << (can_reach_2 ? "可以到达" : "只能到达最近位置") << ": " << nearest_pos_2 << std::endl;
                    
                    // 获取2号设备当前位置进行对比
                    int current_pos_2;
                    if (manager.compete_manage_get_current_position(device_2, &current_pos_2)) {
                        std::cout << "2号设备当前位置: " << current_pos_2 << std::endl;
                    }
                    
                    print_test_result("单个车辆移动模拟测试", final_pos == nearest_pos);
                } else {
                    print_test_result("单个车辆移动模拟测试", false);
                }
            } else {
                std::cout << "车辆移动超时！" << std::endl;
                simulator.stop_movement();
                print_test_result("单个车辆移动模拟测试", false);
            }
        } else {
            std::cout << "车辆启动失败！" << std::endl;
            print_test_result("单个车辆移动模拟测试", false);
        }
        
    } catch (const std::exception& e) {
        std::cout << "单个车辆移动测试异常: " << e.what() << std::endl;
        print_test_result("单个车辆移动模拟测试", false);
    }
}



    

/**
 * @brief 主函数
 */
int main() {
    std::cout << "compete_manage防碰撞管理器增强测试程序启动" << std::endl;
    std::cout << "测试时间: " << __DATE__ << " " << __TIME__ << std::endl;
    std::cout << "新增功能: 多线程车辆移动模拟" << std::endl;
    
    try {
        // 获取管理器实例并初始化
        compete_manage& manager = compete_manage::get_instance();
        manager.compete_manage_init();
        std::cout << "compete_manage初始化完成" << std::endl;
        
        // 执行基础测试
        test_current_position_management();
        test_nearest_position();
        
        // 执行新增的移动模拟测试
        test_single_vehicle_movement();
        
    } catch (const std::exception& e) {
        std::cout << "程序执行异常: " << e.what() << std::endl;
        return -1;
    }
    
    std::cout << "\n" << std::string(50, '=') << std::endl;
    std::cout << "所有测试完成！" << std::endl;
    std::cout << std::string(50, '=') << std::endl;
    
    return 0;
}

/**@file  	   device_manage.hpp
* @brief       本播墙调度系统的地图管理源代码
* @details     NULL
* <AUTHOR>
* @date        2024-10-10
* @version     v1.0.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.1.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2024/10/10  <td>1.0.0    <td>lizhy     <td>初始版本	                   </tr>
* </table>
*
**********************************************************************************
*/




#ifndef __SETTING_HPP__
#define __SETTING_HPP__

#include "share/nlohmann_json/json.hpp"

#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"

#include "share/pb/idl/data_map.pb.h"
#include "share/pb/idl/sys_interface.pb.h"
#include "share/pb/idl/exception.pb.h"

#include <string>
#include <iostream>
#include <vector>
#include <map>
#include <functional>
#include <memory>
#include <unordered_map>
#include <list>
#include <zmq.h>
#include <cppzmq/zmq.hpp>

#include <thread>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <errno.h>
#include <stdexcept>
#include <mutex> 


using namespace std;

typedef enum __dev_feeder_type 
{
    FEEDER_TYPE_NULL = 0,
    FEEDER_TYPE_AUTO = 1,
    FEEDER_TYPE_MANUAL = 2,
} dev_feeder_type ;

typedef enum __task_check_opt
{
    CHECK_POS_NULL = 0,
    CHECK_POS_FSM_CHANGE = 1,
    CHECK_POS_SLOT_POS = 2,
} task_check_opt ;


typedef struct __scheduler_cfg
{
	int feeder_dir;
	dev_feeder_type  feeder_type;
	int scanner_type;
	bool belt_check;
	bool belt_auto_reset;
	bool slot_full_beep;
	bool dev_emerg_beep;
	bool multi_loop_sort;
	int multi_loop_threshold;
	bool slot_report_real;
	bool slot_full_hos;
	bool slot_seal_hos;
	bool slot_hos_bind;
	int task_hb_threshold;
	int task_hb_timeout;
	int task_pos_timeout;
	task_check_opt task_checke_opt;
	int slot_seal_check_timeout;
	
	int train_following_distance;

	int train_max_apply_len;
	int train_calib_max_len;
	int train_calib_speed;

	bool train_speed_auto_adaption;
	int train_speed_straight;
	int train_speed_straight_high;
	int train_speed_arc;
	int train_speed_feeder;
	int train_speed_container;
	int train_speed_change_distance;

	int upload_finish_src;

	int train_apply_threshold_move;
	int train_apply_threshold_calib;

	int feeder_heigth_err_limit;
	int feeder_valid_range;

	bool dev_auto_throw_enable;
	int dev_auto_throw_cont_1;
	int dev_auto_throw_cont_2;


}scheduler_cfg;



typedef struct __platform_cfg
{
	int load_threshold;
	int unload_threshold;
	int opt_y_axis_err;
	int unload_height_ctrl;
}platform_cfg;



typedef struct __gy_camera_client_cfg
{
	int dev_id;
	std::string dev_addr;
	int dev_port;
	int bind_feeder;
	std::string manufacturer;
}gy_camera_client_cfg;


typedef struct __gy_camera_cfg
{
	int camera_cnt;
	std::string server_ip;
	int server_port;
	gy_camera_client_cfg client_cfg[20];
}gy_camera_cfg;



typedef enum __dev_task_apply
{
    TASK_APPLY_NULL = 0,
    TASK_APPLY_FEEDER_POS = 1,
    TASK_APPLY_CAMERA_POS = 2,
} dev_task_apply ;


typedef struct __dev_task_opt_cfg
{
	dev_task_apply apply;
	int apply_timeout;
	std::string task_id_head;
	int task_id_len;
	bool task_id_feeder_flag;
	bool task_id_scanner_flag;
	float task_area_mask;
	int task_confirm_pos_err;
	bool task_excep_force_unpack;
	bool task_queue_enable;
	bool task_single_barcode_multi_package_enable;
}dev_task_opt_cfg;


typedef struct __dev_dyna_cfg
{
	bool dyna_enable;
	int dyna_task_type;
	int dyna_range;
	int dyna_speed_cfg;
	int dyna_acc_cfg;
}_dev_dyna_cfg;

typedef struct __dev_belt_rollback
{
	bool rollback_enable;
	int rollback_type;
	int rollbakc_threshold;
	int rollback_speed_cfg;
}_dev_belt_rollback;




typedef enum
{
	MOVE_TYPE_RESERVE = 0,
	MOVE_TYPE_SINGLE = 1,
	MOVE_TYPE_HARD_LINK = 2,
	MOVE_TYPE_SOFT_LINK = 3,
	MOVE_TYPE_NO_LINK = 4,
	MOVE_TYPE_DYNAMIC_SPEED = 5,		
}__DEV_MOVE_TYPE;


/**
* @brief 根据JSON文件生成地图信息
*/
class setting
{
public:
	
	
	/**@brief  fsm_manager class析构函数
	* @param[in]  NULL
	* @return	  NULL
	*/
	~setting();

	bool setting_init(void);

	bool setting_load_setting_para(void);

	int setting_get_sys_dev_following_distance(void);

	int setting_get_sys_dev_apply_max_len(void);

	int setting_get_sys_dev_apply_max_len_calib(void);

	int setting_get_sys_dev_calib_speed_limit(void);

	scheduler_cfg setting_get_sys_all_cfg_para(void);

	int setting_get_train_apply_threshold_move(void);
	int setting_get_train_apply_threshold_calib(void);

	int setting_get_sys_dev_move_speed_limit(void);

	platform_cfg setting_get_platform_load_para(void);

	bool setting_get_container_full_opt(void);
	bool setting_get_is_container_full_beep(void);

	bool setting_get_container_seal_opt(void);

	bool setting_load_gray_camera_para_data(void);

	dev_feeder_type setting_get_feeder_type(void);

	bool setting_load_task_cfg(void);

	dev_task_opt_cfg setting_get_task_cfg(void);

	gy_camera_cfg setting_get_gray_camera_cfg(void);

	inline bool setting_get_first_power_up_flag() const 
	{
		return m_first_powup_flag; 
	}

	inline bool setting_get_light_curtain_enable() const 
	{
		return m_dev_light_curtain_enable; 
	}

	inline __DEV_MOVE_TYPE setting_get_device_move_type() const 
	{
		return m_dev_move_type; 
	}

	inline bool setting_get_lc_feed_enable() const 
	{
		return m_dev_lc_feed_enable; 
	}

	inline int setting_get_lc_feed_time() const 
	{
		return m_dev_lc_feed_time; 
	}

	inline int setting_get_lc_feed_untrig_time() const 
	{
		return m_dev_lc_feed_untrig_time; 
	}

	inline int setting_get_lc_feed_limit_time() const 
	{
		return m_dev_lc_feed_limit; 
	}

	inline int setting_get_motion_cycle() const 
	{
		return m_dev_motion_cycle; 
	}

	inline int setting_get_maintenance_cycle() const 
	{
		return m_dev_manitenance_timeout; 
	}


	inline int setting_get_maintenance_position() const 
	{
		return m_dev_manitenance_position; 
	}

	inline _dev_dyna_cfg setting_get_dev_dyna_cfg() const 
	{
		return m_dyna_cfg; 
	}

	inline _dev_belt_rollback setting_get_dev_rollback_cfg() const 
	{
		return m_rollback_cfg; 
	}

	inline bool setting_get_dev_task_queue_cfg() const 
	{
		return m_task_cfg.task_queue_enable; 
	}

	inline bool setting_get_dev_auto_throw_cfg() const 
	{
		return m_dev_cfg.dev_auto_throw_enable; 
	}
	
	inline bool setting_get_dev_task_single_barcode_cfg() const 
	{
		return m_task_cfg.task_single_barcode_multi_package_enable; 
	}

	inline int setting_get_dev_unload_pos_offset() const 
	{
		return m_dev_unload_pos; 
	}

	inline bool setting_get_dev_pri_bind_flag() const 
	{
		return m_dev_task_pri_bind; 
	}

	
	static setting *get_instance(void)
    {
        static setting instance;
        return &instance;
    }
	
private:

	scheduler_cfg m_dev_cfg;

	platform_cfg m_platfrom_cfg;

	gy_camera_cfg m_gy_camera_cfg; 

	dev_task_opt_cfg m_task_cfg;

	bool m_first_powup_flag;

	bool m_dev_light_curtain_enable;

	bool m_dev_lc_feed_enable;

	int m_dev_lc_feed_time;
	int m_dev_lc_feed_untrig_time;
	int m_dev_lc_feed_limit;

	int m_dev_motion_cycle;

	__DEV_MOVE_TYPE m_dev_move_type;

	int m_dev_manitenance_timeout;

	int m_dev_manitenance_position;

	_dev_dyna_cfg m_dyna_cfg;
	_dev_belt_rollback m_rollback_cfg;

	int m_dev_unload_pos;

	bool m_dev_task_pri_bind;
	

};







#endif

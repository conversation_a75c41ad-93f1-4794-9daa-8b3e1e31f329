
/**@file  	   device_manage.cpp
* @brief       本播墙调度系统的设备管理接口文件
* @details     NULL
* <AUTHOR>
* @date        2022-02-14
* @version     v1.0.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.1.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/06/25  <td>1.0.0    <td>lizhy     <td>初始版本	                   </tr>
* </table>
*
**********************************************************************************
*/


#include "setting.hpp"
#include "share/global_def.h"

#include "share/nlohmann_json/json.hpp"


#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/sinks/vehicle_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>
#include <spdlog/logger.h>    
#include <spdlog/async.h>

#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"

#include "share/pb/idl/exception.pb.h"

#include "share/pb/idl/data_map.pb.h"
#include "share/pb/idl/data_request.pb.h"

#include "share/pb/idl/sys_interface.pb.h"

#include "share/pb/idl/feeder_interface.pb.h"
#include "share/pb/idl/train_interface.pb.h"
#include "share/pb/idl/container_interface.pb.h"
#include "share/pb/idl/scheduler_interface.pb.h"

#include <zmq.h>
#include <cppzmq/zmq.hpp>

#include <sys/types.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <netdb.h>

#include <fcntl.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <errno.h>
#include <iostream>
#include <stdexcept>
#include <memory>
#include <mutex>          
#include <fstream>

#include "setting.hpp"


using namespace std;

/**@brief  setting class析构函数
* @param[in]  NULL
* @return     NULL
*/
setting::~setting() 
{
	//m_vehicle_dev_list.clear();
	//m_vehicle_sock_list.clear();
}


/**@brief    setting 初始化函数
* @param[in]  NULL
* @return     函数执行结果
* - true      server创建成功
*/
bool setting::setting_init(void) 
{
	if( !setting_load_setting_para() )
	{
		m_dev_cfg.feeder_dir = 1;
		m_dev_cfg.feeder_type = FEEDER_TYPE_NULL;
		m_dev_cfg.scanner_type = 0;
		m_dev_cfg.belt_check = false;
		m_dev_cfg.belt_auto_reset = false;
		m_dev_cfg.slot_full_beep = false;
		m_dev_cfg.dev_emerg_beep = false;
		m_dev_cfg.multi_loop_sort = false;
		m_dev_cfg.multi_loop_threshold = 1;
		m_dev_cfg.slot_report_real = true;
		m_dev_cfg.slot_full_hos = true;
		m_dev_cfg.slot_seal_hos = true;
		m_dev_cfg.slot_hos_bind = false;
		m_dev_cfg.task_hb_threshold = 2000;
		m_dev_cfg.task_hb_timeout = 15000;
		m_dev_cfg.upload_finish_src = 0;


		m_dev_cfg.train_following_distance = 200;
		m_dev_cfg.train_max_apply_len = 3000;
		m_dev_cfg.train_calib_max_len = 400;
		m_dev_cfg.train_calib_speed = 100;

		m_dev_cfg.train_speed_auto_adaption = false;
		m_dev_cfg.train_speed_straight = 300;
		m_dev_cfg.train_speed_straight_high = 600;
		m_dev_cfg.train_speed_arc = 300;
		m_dev_cfg.train_speed_feeder = 300;
		m_dev_cfg.train_speed_container = 300;
		m_dev_cfg.train_speed_change_distance = 1000;

		m_dev_move_type = MOVE_TYPE_RESERVE;
		m_first_powup_flag = true;

	}	
	setting_load_gray_camera_para_data();

	setting_load_task_cfg();

	return true;
}


bool setting::setting_load_setting_para(void) 
{
	std::string file_name;
	std::string home_path = getenv("HOME");

	file_name = home_path + "/auto_sort_high_efficient/cfg_file/device_config.json";
	std::cout << "log>>: dir name curr_dir "<< file_name  << std::endl;
	
    nlohmann::json j;
    ifstream jfile(file_name.c_str());
   
	try
	{
		jfile >> j;;
	}
	catch(nlohmann::detail::exception &fe)
	{
		SPDLOG_ERROR("open file occurs error: {}", fe.what());
		
		return false;
	}
	std::cout << "log>>: dir name curr_dir "<< file_name  << std::endl;
	m_dev_cfg.feeder_dir = j["scheduler_config"]["feeder_dir"];
	m_dev_cfg.feeder_type = (dev_feeder_type)j["scheduler_config"]["feeder_type"];
	m_dev_cfg.scanner_type = j["scheduler_config"]["scanner_type"];
	m_dev_cfg.belt_check = j["scheduler_config"]["belt_check"];
	m_dev_cfg.belt_auto_reset = j["scheduler_config"]["belt_reset"];
	m_dev_cfg.slot_full_beep = j["scheduler_config"]["slot_full_beep"];
	m_dev_cfg.dev_emerg_beep = j["scheduler_config"]["sys_emerg_beep"];
	m_dev_cfg.multi_loop_sort = j["scheduler_config"]["dev_multi_loop_flag"];
	m_dev_cfg.multi_loop_threshold = j["scheduler_config"]["dev_multi_loop"];
	m_dev_cfg.slot_report_real = j["scheduler_config"]["slot_report_real"];
	m_dev_cfg.slot_full_hos = j["scheduler_config"]["slot_full_hos"];
	m_dev_cfg.slot_seal_hos = j["scheduler_config"]["slot_seal_hos"];
	m_dev_cfg.slot_hos_bind = j["scheduler_config"]["slot_hos_bind_flag"];
	m_dev_cfg.task_hb_threshold = j["scheduler_config"]["task_hb_threshold"];
	m_dev_cfg.task_hb_timeout = j["scheduler_config"]["task_hb_timeout"];
	m_dev_cfg.task_pos_timeout = j["scheduler_config"]["task_pos_timeout"];
	m_dev_cfg.task_checke_opt = (task_check_opt)j["scheduler_config"]["task_check_opt"];
	m_dev_cfg.upload_finish_src = j["scheduler_config"]["feed_finish_src"];
	m_dev_cfg.feeder_heigth_err_limit = j["scheduler_config"]["feeder_heigth_err_limit"];
	m_dev_cfg.feeder_valid_range = j["scheduler_config"]["feeder_valid_range"];
	m_dev_cfg.slot_seal_check_timeout = j["scheduler_config"]["slot_seal_check_timeout"];
	m_dev_cfg.dev_auto_throw_enable = j["scheduler_config"]["dev_auto_throw"];
	m_dev_cfg.dev_auto_throw_cont_1 = j["scheduler_config"]["dev_auto_throw_cont_1"];
	m_dev_cfg.dev_auto_throw_cont_2 = j["scheduler_config"]["dev_auto_throw_cont_2"];


	m_dev_cfg.train_following_distance = j["train_movement"]["train_following_distance"];
	m_dev_cfg.train_max_apply_len = j["train_movement"]["train_max_apply_len"];
	m_dev_cfg.train_calib_max_len = j["train_movement"]["train_calib_max_len"];
	m_dev_cfg.train_calib_speed = j["train_movement"]["train_calib_speed"];
	m_dev_cfg.train_speed_auto_adaption = j["train_movement"]["train_speed_auto_adaption"];
	m_dev_cfg.train_speed_straight = j["train_movement"]["train_speed_straight"];
	m_dev_cfg.train_speed_straight_high = j["train_movement"]["train_speed_straight_high"];
	m_dev_cfg.train_speed_arc = j["train_movement"]["train_speed_arc"];
	m_dev_cfg.train_speed_feeder = j["train_movement"]["train_speed_feeder"];
	m_dev_cfg.train_speed_container = j["train_movement"]["train_speed_container"];
	m_dev_cfg.train_speed_change_distance = j["train_movement"]["train_speed_change_distance"];


	m_dev_cfg.train_apply_threshold_move = j["train_movement"]["train_apply_threshold_move"];
	m_dev_cfg.train_apply_threshold_calib = j["train_movement"]["train_apply_threshold_cali"];

	m_platfrom_cfg.load_threshold = j["platform_config"]["load_threshold"];
	m_platfrom_cfg.unload_threshold = j["platform_config"]["unload_threshold"];
	m_platfrom_cfg.opt_y_axis_err =  j["platform_config"]["Y_axis_err"];
	m_platfrom_cfg.unload_height_ctrl = j["platform_config"]["unload_height_ctrl"];
	m_dev_unload_pos = j["platform_config"]["unload_pos_offset"];

	m_first_powup_flag = j["first_powerup_flag"];

	m_dev_move_type = (__DEV_MOVE_TYPE)(j["train_move_type"]);
	m_dev_light_curtain_enable = j["scheduler_config"]["light_curtain_enable"];
	m_dev_lc_feed_enable =  j["scheduler_config"]["light_curtain_feed_enable"];
	m_dev_lc_feed_time = j["scheduler_config"]["light_curtain_feed_time"];
	m_dev_lc_feed_untrig_time = j["scheduler_config"]["light_curtain_feed_untrig_time"];
	m_dev_lc_feed_limit = j["scheduler_config"]["light_curtain_feed_limit_time"];
	m_dev_motion_cycle = j["train_move_cycle"];
	
	m_dev_manitenance_timeout= j["scheduler_config"]["maintencance_timeout"];

	m_dev_manitenance_position= j["scheduler_config"]["maintencance_position"];

	m_dev_task_pri_bind = j["scheduler_config"]["task_pri_bind"];


	m_dyna_cfg.dyna_enable = j["scheduler_enhance_cfg"]["Y_axis_dynamics"]["dynamics_enable"];
	m_dyna_cfg.dyna_task_type = j["scheduler_enhance_cfg"]["Y_axis_dynamics"]["dynamics_task_integration"];
	m_dyna_cfg.dyna_range = j["scheduler_enhance_cfg"]["Y_axis_dynamics"]["dynamics_range"];
	m_dyna_cfg.dyna_speed_cfg = j["scheduler_enhance_cfg"]["Y_axis_dynamics"]["dynamics_speed"];
	m_dyna_cfg.dyna_acc_cfg = j["scheduler_enhance_cfg"]["Y_axis_dynamics"]["dynamics_acc"];


	m_rollback_cfg.rollback_enable = j["scheduler_enhance_cfg"]["belt_roll_back_cfg"]["roll_back_enable"];
	m_rollback_cfg.rollback_type = j["scheduler_enhance_cfg"]["belt_roll_back_cfg"]["roll_back_ctrl_type"];
	m_rollback_cfg.rollbakc_threshold = j["scheduler_enhance_cfg"]["belt_roll_back_cfg"]["range_threshold"];
	m_rollback_cfg.rollback_speed_cfg = j["scheduler_enhance_cfg"]["belt_roll_back_cfg"]["roll_back_speed"];

	return true;

}


int setting::setting_get_sys_dev_following_distance(void)
{
	return m_dev_cfg.train_following_distance;
}


int setting::setting_get_sys_dev_apply_max_len(void)
{
	return m_dev_cfg.train_max_apply_len;
}


int setting::setting_get_sys_dev_apply_max_len_calib(void)
{
	return m_dev_cfg.train_calib_max_len;
}

int setting::setting_get_sys_dev_calib_speed_limit(void)
{
	return m_dev_cfg.train_calib_speed;
}

int setting::setting_get_sys_dev_move_speed_limit(void)
{
	return m_dev_cfg.train_speed_straight;
}


scheduler_cfg setting::setting_get_sys_all_cfg_para(void)
{
	return m_dev_cfg;
}


int setting::setting_get_train_apply_threshold_move(void)
{
	return m_dev_cfg.train_apply_threshold_move;
}


int setting::setting_get_train_apply_threshold_calib(void)
{
	return m_dev_cfg.train_apply_threshold_calib;
}


platform_cfg setting::setting_get_platform_load_para(void)
{
	return m_platfrom_cfg;
}


bool setting::setting_get_container_full_opt(void)
{
	return m_dev_cfg.slot_full_hos;
}

bool setting::setting_get_is_container_full_beep(void)
{
	return m_dev_cfg.slot_full_beep;
}


bool setting::setting_get_container_seal_opt(void)
{
	return m_dev_cfg.slot_seal_hos;
}



bool setting::setting_load_gray_camera_para_data(void)
{
	std::string file_name;
	std::string home_path = getenv("HOME");

	file_name = home_path + "/auto_sort_high_efficient/cfg_file/device_config.json";
	std::cout << "log>>: dir name curr_dir "<< file_name  << std::endl;
	
    nlohmann::json j;
    ifstream jfile(file_name.c_str());
   
	try
	{
		jfile >> j;;
	}
	catch(nlohmann::detail::exception &fe)
	{
		SPDLOG_ERROR("open file occurs error: {}", fe.what());
		
		return false;
	}

	m_gy_camera_cfg.camera_cnt = j["grayscale_camera_cfg"]["camera_group"];
	m_gy_camera_cfg.server_ip = j["grayscale_camera_cfg"]["server_ip"];
	m_gy_camera_cfg.server_port = j["grayscale_camera_cfg"]["server_port"];

	for(int i=0; i<m_gy_camera_cfg.camera_cnt; i++)
	{
		m_gy_camera_cfg.client_cfg[i].dev_id = j["grayscale_camera_cfg"]["camera_detail"][i]["dev_id"];
		m_gy_camera_cfg.client_cfg[i].dev_addr = j["grayscale_camera_cfg"]["camera_detail"][i]["dev_ip"];
		m_gy_camera_cfg.client_cfg[i].dev_port = j["grayscale_camera_cfg"]["camera_detail"][i]["dev_port"];
		m_gy_camera_cfg.client_cfg[i].manufacturer = j["grayscale_camera_cfg"]["camera_detail"][i]["manufacturer"];
	}

	return true;
}

gy_camera_cfg setting::setting_get_gray_camera_cfg(void)
{
	return m_gy_camera_cfg;
}


dev_feeder_type setting::setting_get_feeder_type(void)
{
	return m_dev_cfg.feeder_type;
}



bool setting::setting_load_task_cfg(void)
{
	std::string file_name;
	std::string home_path = getenv("HOME");

	file_name = home_path + "/auto_sort_high_efficient/cfg_file/device_config.json";
	std::cout << "log>>: dir name curr_dir "<< file_name  << std::endl;
	
    nlohmann::json j;
    ifstream jfile(file_name.c_str());
   
	try
	{
		jfile >> j;;
	}
	catch(nlohmann::detail::exception &fe)
	{
		SPDLOG_ERROR("open file occurs error: {}", fe.what());
		
		return false;
	}

	m_task_cfg.apply = (dev_task_apply)j["task_opt_cfg"]["task_apply"];
	m_task_cfg.apply_timeout = j["task_opt_cfg"]["task_apply_timeout"];
	m_task_cfg.task_id_head = j["task_opt_cfg"]["task_id_head"];
	m_task_cfg.task_id_feeder_flag = j["task_opt_cfg"]["task_id_feeder"];
	m_task_cfg.task_id_scanner_flag = j["task_opt_cfg"]["task_id_scanner"];
	m_task_cfg.task_id_len = j["task_opt_cfg"]["task_id_len"];

	int area_temp = j["task_opt_cfg"]["task_area_mask"];

	m_task_cfg.task_area_mask = (float)(area_temp);
	m_task_cfg.task_confirm_pos_err =  j["task_opt_cfg"]["task_confim_pos_error"];
	m_task_cfg.task_excep_force_unpack = j["task_opt_cfg"]["task_excep_force_unpack"];

	m_task_cfg.task_queue_enable = j["task_opt_cfg"]["task_queue_enable"];

	m_task_cfg.task_queue_enable = j["task_opt_cfg"]["task_queue_enable"];
	m_task_cfg.task_single_barcode_multi_package_enable = j["task_opt_cfg"]["single_barcode_multi_package_enable"];
	return true;
}




dev_task_opt_cfg setting::setting_get_task_cfg(void)
{
	return m_task_cfg;
}

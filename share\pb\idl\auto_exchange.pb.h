/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.8 */

#ifndef PB_AUTO_EXCHANGE_PB_H_INCLUDED
#define PB_AUTO_EXCHANGE_PB_H_INCLUDED
#include <pb.h>
#include "sys_interface.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
/* 状态相关 */
typedef enum _dev_pos_calib_state {
    dev_pos_calib_state_STATE_NORMAL = 0,
    dev_pos_calib_state_STATE_ERROR = 1,
    dev_pos_calib_state_STATE_INIT = 3
} dev_pos_calib_state;

typedef enum _auto_exchange_device_state {
    auto_exchange_device_state_DEV_RESERVE = 0,
    auto_exchange_device_state_DEV_INIT = 1,
    auto_exchange_device_state_DEV_NORMAL = 2,
    auto_exchange_device_state_DEV_ERROR = 3,
    auto_exchange_device_state_DEV_FATAL = 4,
    auto_exchange_device_state_DEV_EMERG_STOP = 5,
    auto_exchange_device_state_DEV_UNKNOWN = 6
} auto_exchange_device_state;

typedef enum _auto_exchange_work_state {
    auto_exchange_work_state_WORK_STATE_RESERVE = 0,
    auto_exchange_work_state_WORK_STATE_INIT = 1,
    auto_exchange_work_state_WORK_STATE_CHECK = 2,
    auto_exchange_work_state_WORK_STATE_IDLE = 3,
    auto_exchange_work_state_WORK_STATE_WORK = 4,
    auto_exchange_work_state_WORK_STATE_CALIB = 5,
    auto_exchange_work_state_WORK_STATE_ERROR = 6,
    auto_exchange_work_state_WORK_STATE_FATAL = 7
} auto_exchange_work_state;

typedef enum _auto_exchange_task_type {
    auto_exchange_task_type_TASK_NULL = 0,
    auto_exchange_task_type_MOVE_X_AXIS = 1,
    auto_exchange_task_type_MOVE_Y_AXIS = 2,
    auto_exchange_task_type_MOVE_XY_AXIS = 3,
    auto_exchange_task_type_MOVE_Z1_AXIS = 4,
    auto_exchange_task_type_MOVE_Z2_AXIS = 5,
    auto_exchange_task_type_MOVE_Z3_AXIS = 7,
    auto_exchange_task_type_TASK_INTE_GRAB_MOVE = 8,
    auto_exchange_task_type_TASK_INTE_UNLOAD_MOVE = 9,
    auto_exchange_task_type_TASK_INTE_GRAB_UNLOAD = 10,
    auto_exchange_task_type_TASK_INTE_CALIB = 11
} auto_exchange_task_type;

typedef enum _cmd_type {
    cmd_type_CMD_NULL = 0,
    cmd_type_CMD_EMERG = 1,
    cmd_type_CMD_RESET = 2,
    cmd_type_CMD_REBOOT = 3,
    cmd_type_CMD_START = 4,
    cmd_type_CMD_STOP = 5
} cmd_type;

typedef enum _auto_exchange_dev_task_state {
    auto_exchange_dev_task_state_IDLE = 0,
    auto_exchange_dev_task_state_INIT = 1,
    auto_exchange_dev_task_state_START = 2,
    auto_exchange_dev_task_state_RUNNING = 3,
    auto_exchange_dev_task_state_SUCCEED_OVER = 4,
    auto_exchange_dev_task_state_ERROR = 5
} auto_exchange_dev_task_state;

typedef enum _auto_exchange_ext_task_type {
    auto_exchange_ext_task_type_TASK_RESERVE = 0,
    auto_exchange_ext_task_type_TASK_MOVE = 1,
    auto_exchange_ext_task_type_TASK_GRAB = 2,
    auto_exchange_ext_task_type_TASK_UNLOAD = 3,
    auto_exchange_ext_task_type_TASK_INTE = 4
} auto_exchange_ext_task_type;

typedef enum _auto_exchange_ext_task_cmd_type {
    auto_exchange_ext_task_cmd_type_TASK_CMD_RESERVE = 0,
    auto_exchange_ext_task_cmd_type_TASK_CMD_CANCEL = 1,
    auto_exchange_ext_task_cmd_type_TASK_CMD_RESUME = 2
} auto_exchange_ext_task_cmd_type;

typedef enum _auto_exchange_ext_task_state {
    auto_exchange_ext_task_state_TASK_STATE_RESERVE = 0,
    auto_exchange_ext_task_state_TASK_STATE_IDLE = 1,
    auto_exchange_ext_task_state_TASK_STATE_READY = 2,
    auto_exchange_ext_task_state_TASK_STATE_GRAB_MOVING = 4,
    auto_exchange_ext_task_state_TASK_STATE_GRABBING = 5,
    auto_exchange_ext_task_state_TASK_STATE_GRAB_FINISH = 6,
    auto_exchange_ext_task_state_TASK_STATE_UNLOAD_MOVING = 7,
    auto_exchange_ext_task_state_TASK_STATE_UNLOADING = 8,
    auto_exchange_ext_task_state_TASK_STATE_UNLOAD_FINISH = 9,
    auto_exchange_ext_task_state_TASK_STATE_FINISHED_MANUALLY = 10, /* 分到了收容口 */
    auto_exchange_ext_task_state_TASK_STATE_SUSPEND = 11,
    auto_exchange_ext_task_state_TASK_STATE_REJECT = 12
} auto_exchange_ext_task_state;

/* Struct definitions */
typedef struct _dev_axis_work_state {
    int32_t move_target;
    int32_t curr_pos;
    int32_t ins_speed;
    int32_t curr_speed;
    int32_t contrl_speed;
    int32_t dev_state;
} dev_axis_work_state;

typedef struct _dev_motor_work_state {
    int32_t curr_speed;
    int32_t curr_pos;
    int32_t error_no;
} dev_motor_work_state;

typedef struct _dev_hook_work_state {
    int32_t target_pos;
    int32_t curr_pos;
    int32_t motion_state;
    int32_t motor_err_no;
} dev_hook_work_state;

typedef struct _auto_exchange_dev_state {
    /* 设备基础信息 */
    uint32_t dev_id; /* 设备ID */
    dev_pos_calib_state pos_state; /* 定位状态 */
    auto_exchange_device_state curr_state;
    auto_exchange_work_state work_state; /* 设备工作状态 */
    int32_t dev_error_level; /* 异常等级 */
    uint32_t dev_error_no; /* 车头故障码 */
    uint32_t dev_task_type;
    uint32_t dev_task_state;
    uint32_t dev_sub_task_state;
    uint32_t motion_positon; /* 车辆坐标 */
    bool motion_positon_valid_flag; /* 车辆位置有效标志 */
    int32_t motion_velocity; /* 车辆速度 */
    bool slot_1_good_state;
    bool slot_2_good_state;
    bool has_x_axis_work_state;
    dev_axis_work_state x_axis_work_state;
    bool has_x1_motor_state;
    dev_motor_work_state x1_motor_state;
    bool has_x2_motor_state;
    dev_motor_work_state x2_motor_state;
    bool has_y_axis_work_state;
    dev_axis_work_state y_axis_work_state;
    bool has_y1_motor_state;
    dev_motor_work_state y1_motor_state;
    bool has_y2_motor_state;
    dev_motor_work_state y2_motor_state;
    bool has_z1_hook_state;
    dev_hook_work_state z1_hook_state;
    bool has_z2_hook_state;
    dev_hook_work_state z2_hook_state;
    bool has_z3_hook_state;
    dev_hook_work_state z3_hook_state;
    int32_t current_mileage;
    int32_t encoder_mileage;
} auto_exchange_dev_state;

typedef struct _auto_exchange_agent_state {
    /* 设备基础信息 */
    uint32_t auto_exchange_dev_cnt; /* 车厢数量 */
    uint32_t auto_exchange_curr_valid_dev_cnt;
    component_state agent_work_state; /* 车头运行状态 */
} auto_exchange_agent_state;

typedef struct _auto_exchange_task_move {
    auto_exchange_task_type type;
    uint32_t target;
    uint32_t speed_limit;
    uint32_t acc_limit;
    uint32_t ctrl_object;
    int32_t move_distance;
} auto_exchange_task_move;

typedef struct _auto_exchange_task_grab {
    auto_exchange_task_type type;
    int32_t ctrl_object;
    int32_t acc_limit_loaded;
    int32_t acc_limit_unloaded;
    int32_t speed_limit_loaded;
    int32_t speed_limit_unloaded;
} auto_exchange_task_grab;

typedef struct _auto_exchange_task_grab_move {
    auto_exchange_task_type type;
    int32_t cmd_type;
    int32_t ctrl_object;
    int32_t x_target_pos;
    int32_t y_target_pos;
    int32_t x_acc;
    int32_t x_speed;
    int32_t y_acc;
    int32_t y_speed;
    int32_t z_acc_loaded;
    int32_t z_acc_unloaded;
    int32_t z_speed_loaded;
    int32_t z_speed_unloaded;
} auto_exchange_task_grab_move;

typedef struct _auto_exchange_task_grab_integration {
    auto_exchange_task_type type;
    auto_exchange_task_type cmd_type;
    int32_t ctrl_object;
    int32_t x_grab_pos;
    int32_t y_grab_pos;
    int32_t x_unload_pos;
    int32_t y_unload_pos;
    int32_t z_stroke_length;
    int32_t x_acc;
    int32_t x_speed;
    int32_t y_acc;
    int32_t y_speed;
    int32_t z_acc_loaded;
    int32_t z_acc_unloaded;
    int32_t z_speed_loaded;
    int32_t z_speed_unloaded;
} auto_exchange_task_grab_integration;

typedef struct _auto_exchange_dev_cmd {
    cmd_type cmd;
    uint32_t para;
} auto_exchange_dev_cmd;

typedef struct _auto_exchange_mileage_info {
    int32_t current_mileage; /* 行驶里程参考 */
    int32_t encoder_mileage; /* 行驶里程 */
} auto_exchange_mileage_info;

typedef struct _auto_exchange_task {
    uint32_t sequence;
    uint32_t dev_id;
    uint32_t sub_dev_id;
    pb_size_t which_task;
    union {
        auto_exchange_task_move move; /* 小车移动 */
        auto_exchange_task_grab grab;
        auto_exchange_task_grab_move grab_move;
        auto_exchange_task_grab_integration grab_inte;
        auto_exchange_dev_cmd cmd;
        uint32_t hb_time_sync;
        auto_exchange_mileage_info mil_info;
    } task;
} auto_exchange_task;

typedef struct _auto_exchange_task_state {
    uint32_t dev_id;
    uint32_t sub_dev_id;
    auto_exchange_task_type type;
    auto_exchange_dev_task_state state;
} auto_exchange_task_state;

typedef struct _workstation_ext_state {
    uint32_t workstation_id;
    uint32_t fault_state;
    bool online_state;
    bool dev_idle_state;
} workstation_ext_state;

typedef struct _auto_exchange_ext_state_single {
    uint32_t device_id;
    bool online_state;
    uint32_t speed;
    uint32_t fault_state;
    char ip_addr[16];
    char firmware_version[16];
    char software_version[16];
    int32_t pos_x;
    int32_t pox_y;
    pb_size_t workstation_state_count;
    workstation_ext_state workstation_state[16];
    char motor_state_no[16];
    auto_exchange_work_state motor_state; /* 行走电机状态 */
    uint32_t motor_speed; /* 行走电速度 */
    int32_t curr_mileage;
    int32_t encoder_mileage;
} auto_exchange_ext_state_single;

typedef struct _auto_exchange_ext_state_multi {
    pb_size_t auto_exchanges_count;
    auto_exchange_ext_state_single auto_exchanges[10];
} auto_exchange_ext_state_multi;

typedef struct _auto_exchange_ext_task_state_msg {
    pb_byte_t task_id[80];
    auto_exchange_ext_task_state state;
    uint32_t dev_id;
    uint32_t sub_dev_id;
    uint32_t container; /* 格口号 */
    uint32_t suspend_info;
} auto_exchange_ext_task_state_msg;

typedef struct _auto_exchange_ext_task_msg {
    uint32_t sequence;
    pb_byte_t task_id[80];
    auto_exchange_ext_task_type task_type;
    uint32_t container;
    uint32_t unload_no;
} auto_exchange_ext_task_msg;

typedef struct _auto_exchange_ext_manual_task_msg {
    uint32_t sequence;
    pb_byte_t task_id[80];
    auto_exchange_ext_task_state state;
    auto_exchange_ext_task_cmd_type cmd;
    uint32_t dev_id;
    uint32_t sub_dev_id;
    uint32_t container;
    uint32_t unload_no;
} auto_exchange_ext_manual_task_msg;


#ifdef __cplusplus
extern "C" {
#endif

/* Helper constants for enums */
#define _dev_pos_calib_state_MIN dev_pos_calib_state_STATE_NORMAL
#define _dev_pos_calib_state_MAX dev_pos_calib_state_STATE_INIT
#define _dev_pos_calib_state_ARRAYSIZE ((dev_pos_calib_state)(dev_pos_calib_state_STATE_INIT+1))

#define _auto_exchange_device_state_MIN auto_exchange_device_state_DEV_RESERVE
#define _auto_exchange_device_state_MAX auto_exchange_device_state_DEV_UNKNOWN
#define _auto_exchange_device_state_ARRAYSIZE ((auto_exchange_device_state)(auto_exchange_device_state_DEV_UNKNOWN+1))

#define _auto_exchange_work_state_MIN auto_exchange_work_state_WORK_STATE_RESERVE
#define _auto_exchange_work_state_MAX auto_exchange_work_state_WORK_STATE_FATAL
#define _auto_exchange_work_state_ARRAYSIZE ((auto_exchange_work_state)(auto_exchange_work_state_WORK_STATE_FATAL+1))

#define _auto_exchange_task_type_MIN auto_exchange_task_type_TASK_NULL
#define _auto_exchange_task_type_MAX auto_exchange_task_type_TASK_INTE_CALIB
#define _auto_exchange_task_type_ARRAYSIZE ((auto_exchange_task_type)(auto_exchange_task_type_TASK_INTE_CALIB+1))

#define _cmd_type_MIN cmd_type_CMD_NULL
#define _cmd_type_MAX cmd_type_CMD_STOP
#define _cmd_type_ARRAYSIZE ((cmd_type)(cmd_type_CMD_STOP+1))

#define _auto_exchange_dev_task_state_MIN auto_exchange_dev_task_state_IDLE
#define _auto_exchange_dev_task_state_MAX auto_exchange_dev_task_state_ERROR
#define _auto_exchange_dev_task_state_ARRAYSIZE ((auto_exchange_dev_task_state)(auto_exchange_dev_task_state_ERROR+1))

#define _auto_exchange_ext_task_type_MIN auto_exchange_ext_task_type_TASK_RESERVE
#define _auto_exchange_ext_task_type_MAX auto_exchange_ext_task_type_TASK_INTE
#define _auto_exchange_ext_task_type_ARRAYSIZE ((auto_exchange_ext_task_type)(auto_exchange_ext_task_type_TASK_INTE+1))

#define _auto_exchange_ext_task_cmd_type_MIN auto_exchange_ext_task_cmd_type_TASK_CMD_RESERVE
#define _auto_exchange_ext_task_cmd_type_MAX auto_exchange_ext_task_cmd_type_TASK_CMD_RESUME
#define _auto_exchange_ext_task_cmd_type_ARRAYSIZE ((auto_exchange_ext_task_cmd_type)(auto_exchange_ext_task_cmd_type_TASK_CMD_RESUME+1))

#define _auto_exchange_ext_task_state_MIN auto_exchange_ext_task_state_TASK_STATE_RESERVE
#define _auto_exchange_ext_task_state_MAX auto_exchange_ext_task_state_TASK_STATE_REJECT
#define _auto_exchange_ext_task_state_ARRAYSIZE ((auto_exchange_ext_task_state)(auto_exchange_ext_task_state_TASK_STATE_REJECT+1))




#define auto_exchange_dev_state_pos_state_ENUMTYPE dev_pos_calib_state
#define auto_exchange_dev_state_curr_state_ENUMTYPE auto_exchange_device_state
#define auto_exchange_dev_state_work_state_ENUMTYPE auto_exchange_work_state

#define auto_exchange_agent_state_agent_work_state_ENUMTYPE component_state

#define auto_exchange_task_move_type_ENUMTYPE auto_exchange_task_type

#define auto_exchange_task_grab_type_ENUMTYPE auto_exchange_task_type

#define auto_exchange_task_grab_move_type_ENUMTYPE auto_exchange_task_type

#define auto_exchange_task_grab_integration_type_ENUMTYPE auto_exchange_task_type
#define auto_exchange_task_grab_integration_cmd_type_ENUMTYPE auto_exchange_task_type

#define auto_exchange_dev_cmd_cmd_ENUMTYPE cmd_type



#define auto_exchange_task_state_type_ENUMTYPE auto_exchange_task_type
#define auto_exchange_task_state_state_ENUMTYPE auto_exchange_dev_task_state


#define auto_exchange_ext_state_single_motor_state_ENUMTYPE auto_exchange_work_state


#define auto_exchange_ext_task_state_msg_state_ENUMTYPE auto_exchange_ext_task_state

#define auto_exchange_ext_task_msg_task_type_ENUMTYPE auto_exchange_ext_task_type

#define auto_exchange_ext_manual_task_msg_state_ENUMTYPE auto_exchange_ext_task_state
#define auto_exchange_ext_manual_task_msg_cmd_ENUMTYPE auto_exchange_ext_task_cmd_type


/* Initializer values for message structs */
#define dev_axis_work_state_init_default         {0, 0, 0, 0, 0, 0}
#define dev_motor_work_state_init_default        {0, 0, 0}
#define dev_hook_work_state_init_default         {0, 0, 0, 0}
#define auto_exchange_dev_state_init_default     {0, _dev_pos_calib_state_MIN, _auto_exchange_device_state_MIN, _auto_exchange_work_state_MIN, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, dev_axis_work_state_init_default, false, dev_motor_work_state_init_default, false, dev_motor_work_state_init_default, false, dev_axis_work_state_init_default, false, dev_motor_work_state_init_default, false, dev_motor_work_state_init_default, false, dev_hook_work_state_init_default, false, dev_hook_work_state_init_default, false, dev_hook_work_state_init_default, 0, 0}
#define auto_exchange_agent_state_init_default   {0, 0, _component_state_MIN}
#define auto_exchange_task_move_init_default     {_auto_exchange_task_type_MIN, 0, 0, 0, 0, 0}
#define auto_exchange_task_grab_init_default     {_auto_exchange_task_type_MIN, 0, 0, 0, 0, 0}
#define auto_exchange_task_grab_move_init_default {_auto_exchange_task_type_MIN, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}
#define auto_exchange_task_grab_integration_init_default {_auto_exchange_task_type_MIN, _auto_exchange_task_type_MIN, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}
#define auto_exchange_dev_cmd_init_default       {_cmd_type_MIN, 0}
#define auto_exchange_mileage_info_init_default  {0, 0}
#define auto_exchange_task_init_default          {0, 0, 0, 0, {auto_exchange_task_move_init_default}}
#define auto_exchange_task_state_init_default    {0, 0, _auto_exchange_task_type_MIN, _auto_exchange_dev_task_state_MIN}
#define workstation_ext_state_init_default       {0, 0, 0, 0}
#define auto_exchange_ext_state_single_init_default {0, 0, 0, 0, "", "", "", 0, 0, 0, {workstation_ext_state_init_default, workstation_ext_state_init_default, workstation_ext_state_init_default, workstation_ext_state_init_default, workstation_ext_state_init_default, workstation_ext_state_init_default, workstation_ext_state_init_default, workstation_ext_state_init_default, workstation_ext_state_init_default, workstation_ext_state_init_default, workstation_ext_state_init_default, workstation_ext_state_init_default, workstation_ext_state_init_default, workstation_ext_state_init_default, workstation_ext_state_init_default, workstation_ext_state_init_default}, "", _auto_exchange_work_state_MIN, 0, 0, 0}
#define auto_exchange_ext_state_multi_init_default {0, {auto_exchange_ext_state_single_init_default, auto_exchange_ext_state_single_init_default, auto_exchange_ext_state_single_init_default, auto_exchange_ext_state_single_init_default, auto_exchange_ext_state_single_init_default, auto_exchange_ext_state_single_init_default, auto_exchange_ext_state_single_init_default, auto_exchange_ext_state_single_init_default, auto_exchange_ext_state_single_init_default, auto_exchange_ext_state_single_init_default}}
#define auto_exchange_ext_task_state_msg_init_default {{0}, _auto_exchange_ext_task_state_MIN, 0, 0, 0, 0}
#define auto_exchange_ext_task_msg_init_default  {0, {0}, _auto_exchange_ext_task_type_MIN, 0, 0}
#define auto_exchange_ext_manual_task_msg_init_default {0, {0}, _auto_exchange_ext_task_state_MIN, _auto_exchange_ext_task_cmd_type_MIN, 0, 0, 0, 0}
#define dev_axis_work_state_init_zero            {0, 0, 0, 0, 0, 0}
#define dev_motor_work_state_init_zero           {0, 0, 0}
#define dev_hook_work_state_init_zero            {0, 0, 0, 0}
#define auto_exchange_dev_state_init_zero        {0, _dev_pos_calib_state_MIN, _auto_exchange_device_state_MIN, _auto_exchange_work_state_MIN, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, false, dev_axis_work_state_init_zero, false, dev_motor_work_state_init_zero, false, dev_motor_work_state_init_zero, false, dev_axis_work_state_init_zero, false, dev_motor_work_state_init_zero, false, dev_motor_work_state_init_zero, false, dev_hook_work_state_init_zero, false, dev_hook_work_state_init_zero, false, dev_hook_work_state_init_zero, 0, 0}
#define auto_exchange_agent_state_init_zero      {0, 0, _component_state_MIN}
#define auto_exchange_task_move_init_zero        {_auto_exchange_task_type_MIN, 0, 0, 0, 0, 0}
#define auto_exchange_task_grab_init_zero        {_auto_exchange_task_type_MIN, 0, 0, 0, 0, 0}
#define auto_exchange_task_grab_move_init_zero   {_auto_exchange_task_type_MIN, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}
#define auto_exchange_task_grab_integration_init_zero {_auto_exchange_task_type_MIN, _auto_exchange_task_type_MIN, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}
#define auto_exchange_dev_cmd_init_zero          {_cmd_type_MIN, 0}
#define auto_exchange_mileage_info_init_zero     {0, 0}
#define auto_exchange_task_init_zero             {0, 0, 0, 0, {auto_exchange_task_move_init_zero}}
#define auto_exchange_task_state_init_zero       {0, 0, _auto_exchange_task_type_MIN, _auto_exchange_dev_task_state_MIN}
#define workstation_ext_state_init_zero          {0, 0, 0, 0}
#define auto_exchange_ext_state_single_init_zero {0, 0, 0, 0, "", "", "", 0, 0, 0, {workstation_ext_state_init_zero, workstation_ext_state_init_zero, workstation_ext_state_init_zero, workstation_ext_state_init_zero, workstation_ext_state_init_zero, workstation_ext_state_init_zero, workstation_ext_state_init_zero, workstation_ext_state_init_zero, workstation_ext_state_init_zero, workstation_ext_state_init_zero, workstation_ext_state_init_zero, workstation_ext_state_init_zero, workstation_ext_state_init_zero, workstation_ext_state_init_zero, workstation_ext_state_init_zero, workstation_ext_state_init_zero}, "", _auto_exchange_work_state_MIN, 0, 0, 0}
#define auto_exchange_ext_state_multi_init_zero  {0, {auto_exchange_ext_state_single_init_zero, auto_exchange_ext_state_single_init_zero, auto_exchange_ext_state_single_init_zero, auto_exchange_ext_state_single_init_zero, auto_exchange_ext_state_single_init_zero, auto_exchange_ext_state_single_init_zero, auto_exchange_ext_state_single_init_zero, auto_exchange_ext_state_single_init_zero, auto_exchange_ext_state_single_init_zero, auto_exchange_ext_state_single_init_zero}}
#define auto_exchange_ext_task_state_msg_init_zero {{0}, _auto_exchange_ext_task_state_MIN, 0, 0, 0, 0}
#define auto_exchange_ext_task_msg_init_zero     {0, {0}, _auto_exchange_ext_task_type_MIN, 0, 0}
#define auto_exchange_ext_manual_task_msg_init_zero {0, {0}, _auto_exchange_ext_task_state_MIN, _auto_exchange_ext_task_cmd_type_MIN, 0, 0, 0, 0}

/* Field tags (for use in manual encoding/decoding) */
#define dev_axis_work_state_move_target_tag      1
#define dev_axis_work_state_curr_pos_tag         2
#define dev_axis_work_state_ins_speed_tag        3
#define dev_axis_work_state_curr_speed_tag       4
#define dev_axis_work_state_contrl_speed_tag     5
#define dev_axis_work_state_dev_state_tag        6
#define dev_motor_work_state_curr_speed_tag      1
#define dev_motor_work_state_curr_pos_tag        2
#define dev_motor_work_state_error_no_tag        3
#define dev_hook_work_state_target_pos_tag       1
#define dev_hook_work_state_curr_pos_tag         2
#define dev_hook_work_state_motion_state_tag     3
#define dev_hook_work_state_motor_err_no_tag     4
#define auto_exchange_dev_state_dev_id_tag       1
#define auto_exchange_dev_state_pos_state_tag    2
#define auto_exchange_dev_state_curr_state_tag   3
#define auto_exchange_dev_state_work_state_tag   4
#define auto_exchange_dev_state_dev_error_level_tag 5
#define auto_exchange_dev_state_dev_error_no_tag 6
#define auto_exchange_dev_state_dev_task_type_tag 7
#define auto_exchange_dev_state_dev_task_state_tag 8
#define auto_exchange_dev_state_dev_sub_task_state_tag 9
#define auto_exchange_dev_state_motion_positon_tag 10
#define auto_exchange_dev_state_motion_positon_valid_flag_tag 11
#define auto_exchange_dev_state_motion_velocity_tag 12
#define auto_exchange_dev_state_slot_1_good_state_tag 13
#define auto_exchange_dev_state_slot_2_good_state_tag 14
#define auto_exchange_dev_state_x_axis_work_state_tag 15
#define auto_exchange_dev_state_x1_motor_state_tag 16
#define auto_exchange_dev_state_x2_motor_state_tag 17
#define auto_exchange_dev_state_y_axis_work_state_tag 18
#define auto_exchange_dev_state_y1_motor_state_tag 19
#define auto_exchange_dev_state_y2_motor_state_tag 20
#define auto_exchange_dev_state_z1_hook_state_tag 21
#define auto_exchange_dev_state_z2_hook_state_tag 22
#define auto_exchange_dev_state_z3_hook_state_tag 23
#define auto_exchange_dev_state_current_mileage_tag 24
#define auto_exchange_dev_state_encoder_mileage_tag 25
#define auto_exchange_agent_state_auto_exchange_dev_cnt_tag 1
#define auto_exchange_agent_state_auto_exchange_curr_valid_dev_cnt_tag 2
#define auto_exchange_agent_state_agent_work_state_tag 3
#define auto_exchange_task_move_type_tag         1
#define auto_exchange_task_move_target_tag       2
#define auto_exchange_task_move_speed_limit_tag  3
#define auto_exchange_task_move_acc_limit_tag    4
#define auto_exchange_task_move_ctrl_object_tag  5
#define auto_exchange_task_move_move_distance_tag 6
#define auto_exchange_task_grab_type_tag         1
#define auto_exchange_task_grab_ctrl_object_tag  2
#define auto_exchange_task_grab_acc_limit_loaded_tag 3
#define auto_exchange_task_grab_acc_limit_unloaded_tag 4
#define auto_exchange_task_grab_speed_limit_loaded_tag 5
#define auto_exchange_task_grab_speed_limit_unloaded_tag 6
#define auto_exchange_task_grab_move_type_tag    1
#define auto_exchange_task_grab_move_cmd_type_tag 2
#define auto_exchange_task_grab_move_ctrl_object_tag 3
#define auto_exchange_task_grab_move_x_target_pos_tag 4
#define auto_exchange_task_grab_move_y_target_pos_tag 5
#define auto_exchange_task_grab_move_x_acc_tag   6
#define auto_exchange_task_grab_move_x_speed_tag 7
#define auto_exchange_task_grab_move_y_acc_tag   8
#define auto_exchange_task_grab_move_y_speed_tag 9
#define auto_exchange_task_grab_move_z_acc_loaded_tag 10
#define auto_exchange_task_grab_move_z_acc_unloaded_tag 11
#define auto_exchange_task_grab_move_z_speed_loaded_tag 12
#define auto_exchange_task_grab_move_z_speed_unloaded_tag 13
#define auto_exchange_task_grab_integration_type_tag 1
#define auto_exchange_task_grab_integration_cmd_type_tag 2
#define auto_exchange_task_grab_integration_ctrl_object_tag 3
#define auto_exchange_task_grab_integration_x_grab_pos_tag 4
#define auto_exchange_task_grab_integration_y_grab_pos_tag 5
#define auto_exchange_task_grab_integration_x_unload_pos_tag 6
#define auto_exchange_task_grab_integration_y_unload_pos_tag 7
#define auto_exchange_task_grab_integration_z_stroke_length_tag 8
#define auto_exchange_task_grab_integration_x_acc_tag 9
#define auto_exchange_task_grab_integration_x_speed_tag 10
#define auto_exchange_task_grab_integration_y_acc_tag 11
#define auto_exchange_task_grab_integration_y_speed_tag 12
#define auto_exchange_task_grab_integration_z_acc_loaded_tag 13
#define auto_exchange_task_grab_integration_z_acc_unloaded_tag 14
#define auto_exchange_task_grab_integration_z_speed_loaded_tag 15
#define auto_exchange_task_grab_integration_z_speed_unloaded_tag 16
#define auto_exchange_dev_cmd_cmd_tag            1
#define auto_exchange_dev_cmd_para_tag           2
#define auto_exchange_mileage_info_current_mileage_tag 1
#define auto_exchange_mileage_info_encoder_mileage_tag 2
#define auto_exchange_task_sequence_tag          1
#define auto_exchange_task_dev_id_tag            2
#define auto_exchange_task_sub_dev_id_tag        3
#define auto_exchange_task_move_tag              4
#define auto_exchange_task_grab_tag              5
#define auto_exchange_task_grab_move_tag         6
#define auto_exchange_task_grab_inte_tag         7
#define auto_exchange_task_cmd_tag               8
#define auto_exchange_task_hb_time_sync_tag      9
#define auto_exchange_task_mil_info_tag          10
#define auto_exchange_task_state_dev_id_tag      1
#define auto_exchange_task_state_sub_dev_id_tag  2
#define auto_exchange_task_state_type_tag        3
#define auto_exchange_task_state_state_tag       4
#define workstation_ext_state_workstation_id_tag 1
#define workstation_ext_state_fault_state_tag    2
#define workstation_ext_state_online_state_tag   3
#define workstation_ext_state_dev_idle_state_tag 4
#define auto_exchange_ext_state_single_device_id_tag 1
#define auto_exchange_ext_state_single_online_state_tag 2
#define auto_exchange_ext_state_single_speed_tag 3
#define auto_exchange_ext_state_single_fault_state_tag 4
#define auto_exchange_ext_state_single_ip_addr_tag 5
#define auto_exchange_ext_state_single_firmware_version_tag 6
#define auto_exchange_ext_state_single_software_version_tag 7
#define auto_exchange_ext_state_single_pos_x_tag 8
#define auto_exchange_ext_state_single_pox_y_tag 9
#define auto_exchange_ext_state_single_workstation_state_tag 10
#define auto_exchange_ext_state_single_motor_state_no_tag 11
#define auto_exchange_ext_state_single_motor_state_tag 12
#define auto_exchange_ext_state_single_motor_speed_tag 13
#define auto_exchange_ext_state_single_curr_mileage_tag 14
#define auto_exchange_ext_state_single_encoder_mileage_tag 15
#define auto_exchange_ext_state_multi_auto_exchanges_tag 1
#define auto_exchange_ext_task_state_msg_task_id_tag 1
#define auto_exchange_ext_task_state_msg_state_tag 2
#define auto_exchange_ext_task_state_msg_dev_id_tag 3
#define auto_exchange_ext_task_state_msg_sub_dev_id_tag 4
#define auto_exchange_ext_task_state_msg_container_tag 5
#define auto_exchange_ext_task_state_msg_suspend_info_tag 6
#define auto_exchange_ext_task_msg_sequence_tag  1
#define auto_exchange_ext_task_msg_task_id_tag   2
#define auto_exchange_ext_task_msg_task_type_tag 3
#define auto_exchange_ext_task_msg_container_tag 4
#define auto_exchange_ext_task_msg_unload_no_tag 5
#define auto_exchange_ext_manual_task_msg_sequence_tag 1
#define auto_exchange_ext_manual_task_msg_task_id_tag 2
#define auto_exchange_ext_manual_task_msg_state_tag 3
#define auto_exchange_ext_manual_task_msg_cmd_tag 4
#define auto_exchange_ext_manual_task_msg_dev_id_tag 5
#define auto_exchange_ext_manual_task_msg_sub_dev_id_tag 6
#define auto_exchange_ext_manual_task_msg_container_tag 7
#define auto_exchange_ext_manual_task_msg_unload_no_tag 8

/* Struct field encoding specification for nanopb */
#define dev_axis_work_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    move_target,       1) \
X(a, STATIC,   SINGULAR, INT32,    curr_pos,          2) \
X(a, STATIC,   SINGULAR, INT32,    ins_speed,         3) \
X(a, STATIC,   SINGULAR, INT32,    curr_speed,        4) \
X(a, STATIC,   SINGULAR, INT32,    contrl_speed,      5) \
X(a, STATIC,   SINGULAR, INT32,    dev_state,         6)
#define dev_axis_work_state_CALLBACK NULL
#define dev_axis_work_state_DEFAULT NULL

#define dev_motor_work_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    curr_speed,        1) \
X(a, STATIC,   SINGULAR, INT32,    curr_pos,          2) \
X(a, STATIC,   SINGULAR, INT32,    error_no,          3)
#define dev_motor_work_state_CALLBACK NULL
#define dev_motor_work_state_DEFAULT NULL

#define dev_hook_work_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    target_pos,        1) \
X(a, STATIC,   SINGULAR, INT32,    curr_pos,          2) \
X(a, STATIC,   SINGULAR, INT32,    motion_state,      3) \
X(a, STATIC,   SINGULAR, INT32,    motor_err_no,      4)
#define dev_hook_work_state_CALLBACK NULL
#define dev_hook_work_state_DEFAULT NULL

#define auto_exchange_dev_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   dev_id,            1) \
X(a, STATIC,   SINGULAR, UENUM,    pos_state,         2) \
X(a, STATIC,   SINGULAR, UENUM,    curr_state,        3) \
X(a, STATIC,   SINGULAR, UENUM,    work_state,        4) \
X(a, STATIC,   SINGULAR, INT32,    dev_error_level,   5) \
X(a, STATIC,   SINGULAR, UINT32,   dev_error_no,      6) \
X(a, STATIC,   SINGULAR, UINT32,   dev_task_type,     7) \
X(a, STATIC,   SINGULAR, UINT32,   dev_task_state,    8) \
X(a, STATIC,   SINGULAR, UINT32,   dev_sub_task_state,   9) \
X(a, STATIC,   SINGULAR, UINT32,   motion_positon,   10) \
X(a, STATIC,   SINGULAR, BOOL,     motion_positon_valid_flag,  11) \
X(a, STATIC,   SINGULAR, INT32,    motion_velocity,  12) \
X(a, STATIC,   SINGULAR, BOOL,     slot_1_good_state,  13) \
X(a, STATIC,   SINGULAR, BOOL,     slot_2_good_state,  14) \
X(a, STATIC,   OPTIONAL, MESSAGE,  x_axis_work_state,  15) \
X(a, STATIC,   OPTIONAL, MESSAGE,  x1_motor_state,   16) \
X(a, STATIC,   OPTIONAL, MESSAGE,  x2_motor_state,   17) \
X(a, STATIC,   OPTIONAL, MESSAGE,  y_axis_work_state,  18) \
X(a, STATIC,   OPTIONAL, MESSAGE,  y1_motor_state,   19) \
X(a, STATIC,   OPTIONAL, MESSAGE,  y2_motor_state,   20) \
X(a, STATIC,   OPTIONAL, MESSAGE,  z1_hook_state,    21) \
X(a, STATIC,   OPTIONAL, MESSAGE,  z2_hook_state,    22) \
X(a, STATIC,   OPTIONAL, MESSAGE,  z3_hook_state,    23) \
X(a, STATIC,   SINGULAR, INT32,    current_mileage,  24) \
X(a, STATIC,   SINGULAR, INT32,    encoder_mileage,  25)
#define auto_exchange_dev_state_CALLBACK NULL
#define auto_exchange_dev_state_DEFAULT NULL
#define auto_exchange_dev_state_x_axis_work_state_MSGTYPE dev_axis_work_state
#define auto_exchange_dev_state_x1_motor_state_MSGTYPE dev_motor_work_state
#define auto_exchange_dev_state_x2_motor_state_MSGTYPE dev_motor_work_state
#define auto_exchange_dev_state_y_axis_work_state_MSGTYPE dev_axis_work_state
#define auto_exchange_dev_state_y1_motor_state_MSGTYPE dev_motor_work_state
#define auto_exchange_dev_state_y2_motor_state_MSGTYPE dev_motor_work_state
#define auto_exchange_dev_state_z1_hook_state_MSGTYPE dev_hook_work_state
#define auto_exchange_dev_state_z2_hook_state_MSGTYPE dev_hook_work_state
#define auto_exchange_dev_state_z3_hook_state_MSGTYPE dev_hook_work_state

#define auto_exchange_agent_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   auto_exchange_dev_cnt,   1) \
X(a, STATIC,   SINGULAR, UINT32,   auto_exchange_curr_valid_dev_cnt,   2) \
X(a, STATIC,   SINGULAR, UENUM,    agent_work_state,   3)
#define auto_exchange_agent_state_CALLBACK NULL
#define auto_exchange_agent_state_DEFAULT NULL

#define auto_exchange_task_move_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    type,              1) \
X(a, STATIC,   SINGULAR, UINT32,   target,            2) \
X(a, STATIC,   SINGULAR, UINT32,   speed_limit,       3) \
X(a, STATIC,   SINGULAR, UINT32,   acc_limit,         4) \
X(a, STATIC,   SINGULAR, UINT32,   ctrl_object,       5) \
X(a, STATIC,   SINGULAR, INT32,    move_distance,     6)
#define auto_exchange_task_move_CALLBACK NULL
#define auto_exchange_task_move_DEFAULT NULL

#define auto_exchange_task_grab_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    type,              1) \
X(a, STATIC,   SINGULAR, INT32,    ctrl_object,       2) \
X(a, STATIC,   SINGULAR, INT32,    acc_limit_loaded,   3) \
X(a, STATIC,   SINGULAR, INT32,    acc_limit_unloaded,   4) \
X(a, STATIC,   SINGULAR, INT32,    speed_limit_loaded,   5) \
X(a, STATIC,   SINGULAR, INT32,    speed_limit_unloaded,   6)
#define auto_exchange_task_grab_CALLBACK NULL
#define auto_exchange_task_grab_DEFAULT NULL

#define auto_exchange_task_grab_move_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    type,              1) \
X(a, STATIC,   SINGULAR, INT32,    cmd_type,          2) \
X(a, STATIC,   SINGULAR, INT32,    ctrl_object,       3) \
X(a, STATIC,   SINGULAR, INT32,    x_target_pos,      4) \
X(a, STATIC,   SINGULAR, INT32,    y_target_pos,      5) \
X(a, STATIC,   SINGULAR, INT32,    x_acc,             6) \
X(a, STATIC,   SINGULAR, INT32,    x_speed,           7) \
X(a, STATIC,   SINGULAR, INT32,    y_acc,             8) \
X(a, STATIC,   SINGULAR, INT32,    y_speed,           9) \
X(a, STATIC,   SINGULAR, INT32,    z_acc_loaded,     10) \
X(a, STATIC,   SINGULAR, INT32,    z_acc_unloaded,   11) \
X(a, STATIC,   SINGULAR, INT32,    z_speed_loaded,   12) \
X(a, STATIC,   SINGULAR, INT32,    z_speed_unloaded,  13)
#define auto_exchange_task_grab_move_CALLBACK NULL
#define auto_exchange_task_grab_move_DEFAULT NULL

#define auto_exchange_task_grab_integration_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    type,              1) \
X(a, STATIC,   SINGULAR, UENUM,    cmd_type,          2) \
X(a, STATIC,   SINGULAR, INT32,    ctrl_object,       3) \
X(a, STATIC,   SINGULAR, INT32,    x_grab_pos,        4) \
X(a, STATIC,   SINGULAR, INT32,    y_grab_pos,        5) \
X(a, STATIC,   SINGULAR, INT32,    x_unload_pos,      6) \
X(a, STATIC,   SINGULAR, INT32,    y_unload_pos,      7) \
X(a, STATIC,   SINGULAR, INT32,    z_stroke_length,   8) \
X(a, STATIC,   SINGULAR, INT32,    x_acc,             9) \
X(a, STATIC,   SINGULAR, INT32,    x_speed,          10) \
X(a, STATIC,   SINGULAR, INT32,    y_acc,            11) \
X(a, STATIC,   SINGULAR, INT32,    y_speed,          12) \
X(a, STATIC,   SINGULAR, INT32,    z_acc_loaded,     13) \
X(a, STATIC,   SINGULAR, INT32,    z_acc_unloaded,   14) \
X(a, STATIC,   SINGULAR, INT32,    z_speed_loaded,   15) \
X(a, STATIC,   SINGULAR, INT32,    z_speed_unloaded,  16)
#define auto_exchange_task_grab_integration_CALLBACK NULL
#define auto_exchange_task_grab_integration_DEFAULT NULL

#define auto_exchange_dev_cmd_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UENUM,    cmd,               1) \
X(a, STATIC,   SINGULAR, UINT32,   para,              2)
#define auto_exchange_dev_cmd_CALLBACK NULL
#define auto_exchange_dev_cmd_DEFAULT NULL

#define auto_exchange_mileage_info_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, INT32,    current_mileage,   1) \
X(a, STATIC,   SINGULAR, INT32,    encoder_mileage,   2)
#define auto_exchange_mileage_info_CALLBACK NULL
#define auto_exchange_mileage_info_DEFAULT NULL

#define auto_exchange_task_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   sequence,          1) \
X(a, STATIC,   SINGULAR, UINT32,   dev_id,            2) \
X(a, STATIC,   SINGULAR, UINT32,   sub_dev_id,        3) \
X(a, STATIC,   ONEOF,    MESSAGE,  (task,move,task.move),   4) \
X(a, STATIC,   ONEOF,    MESSAGE,  (task,grab,task.grab),   5) \
X(a, STATIC,   ONEOF,    MESSAGE,  (task,grab_move,task.grab_move),   6) \
X(a, STATIC,   ONEOF,    MESSAGE,  (task,grab_inte,task.grab_inte),   7) \
X(a, STATIC,   ONEOF,    MESSAGE,  (task,cmd,task.cmd),   8) \
X(a, STATIC,   ONEOF,    UINT32,   (task,hb_time_sync,task.hb_time_sync),   9) \
X(a, STATIC,   ONEOF,    MESSAGE,  (task,mil_info,task.mil_info),  10)
#define auto_exchange_task_CALLBACK NULL
#define auto_exchange_task_DEFAULT NULL
#define auto_exchange_task_task_move_MSGTYPE auto_exchange_task_move
#define auto_exchange_task_task_grab_MSGTYPE auto_exchange_task_grab
#define auto_exchange_task_task_grab_move_MSGTYPE auto_exchange_task_grab_move
#define auto_exchange_task_task_grab_inte_MSGTYPE auto_exchange_task_grab_integration
#define auto_exchange_task_task_cmd_MSGTYPE auto_exchange_dev_cmd
#define auto_exchange_task_task_mil_info_MSGTYPE auto_exchange_mileage_info

#define auto_exchange_task_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   dev_id,            1) \
X(a, STATIC,   SINGULAR, UINT32,   sub_dev_id,        2) \
X(a, STATIC,   SINGULAR, UENUM,    type,              3) \
X(a, STATIC,   SINGULAR, UENUM,    state,             4)
#define auto_exchange_task_state_CALLBACK NULL
#define auto_exchange_task_state_DEFAULT NULL

#define workstation_ext_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   workstation_id,    1) \
X(a, STATIC,   SINGULAR, UINT32,   fault_state,       2) \
X(a, STATIC,   SINGULAR, BOOL,     online_state,      3) \
X(a, STATIC,   SINGULAR, BOOL,     dev_idle_state,    4)
#define workstation_ext_state_CALLBACK NULL
#define workstation_ext_state_DEFAULT NULL

#define auto_exchange_ext_state_single_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   device_id,         1) \
X(a, STATIC,   SINGULAR, BOOL,     online_state,      2) \
X(a, STATIC,   SINGULAR, UINT32,   speed,             3) \
X(a, STATIC,   SINGULAR, UINT32,   fault_state,       4) \
X(a, STATIC,   SINGULAR, STRING,   ip_addr,           5) \
X(a, STATIC,   SINGULAR, STRING,   firmware_version,   6) \
X(a, STATIC,   SINGULAR, STRING,   software_version,   7) \
X(a, STATIC,   SINGULAR, INT32,    pos_x,             8) \
X(a, STATIC,   SINGULAR, INT32,    pox_y,             9) \
X(a, STATIC,   REPEATED, MESSAGE,  workstation_state,  10) \
X(a, STATIC,   SINGULAR, STRING,   motor_state_no,   11) \
X(a, STATIC,   SINGULAR, UENUM,    motor_state,      12) \
X(a, STATIC,   SINGULAR, UINT32,   motor_speed,      13) \
X(a, STATIC,   SINGULAR, INT32,    curr_mileage,     14) \
X(a, STATIC,   SINGULAR, INT32,    encoder_mileage,  15)
#define auto_exchange_ext_state_single_CALLBACK NULL
#define auto_exchange_ext_state_single_DEFAULT NULL
#define auto_exchange_ext_state_single_workstation_state_MSGTYPE workstation_ext_state

#define auto_exchange_ext_state_multi_FIELDLIST(X, a) \
X(a, STATIC,   REPEATED, MESSAGE,  auto_exchanges,    1)
#define auto_exchange_ext_state_multi_CALLBACK NULL
#define auto_exchange_ext_state_multi_DEFAULT NULL
#define auto_exchange_ext_state_multi_auto_exchanges_MSGTYPE auto_exchange_ext_state_single

#define auto_exchange_ext_task_state_msg_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, FIXED_LENGTH_BYTES, task_id,           1) \
X(a, STATIC,   SINGULAR, UENUM,    state,             2) \
X(a, STATIC,   SINGULAR, UINT32,   dev_id,            3) \
X(a, STATIC,   SINGULAR, UINT32,   sub_dev_id,        4) \
X(a, STATIC,   SINGULAR, UINT32,   container,         5) \
X(a, STATIC,   SINGULAR, UINT32,   suspend_info,      6)
#define auto_exchange_ext_task_state_msg_CALLBACK NULL
#define auto_exchange_ext_task_state_msg_DEFAULT NULL

#define auto_exchange_ext_task_msg_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   sequence,          1) \
X(a, STATIC,   SINGULAR, FIXED_LENGTH_BYTES, task_id,           2) \
X(a, STATIC,   SINGULAR, UENUM,    task_type,         3) \
X(a, STATIC,   SINGULAR, UINT32,   container,         4) \
X(a, STATIC,   SINGULAR, UINT32,   unload_no,         5)
#define auto_exchange_ext_task_msg_CALLBACK NULL
#define auto_exchange_ext_task_msg_DEFAULT NULL

#define auto_exchange_ext_manual_task_msg_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   sequence,          1) \
X(a, STATIC,   SINGULAR, FIXED_LENGTH_BYTES, task_id,           2) \
X(a, STATIC,   SINGULAR, UENUM,    state,             3) \
X(a, STATIC,   SINGULAR, UENUM,    cmd,               4) \
X(a, STATIC,   SINGULAR, UINT32,   dev_id,            5) \
X(a, STATIC,   SINGULAR, UINT32,   sub_dev_id,        6) \
X(a, STATIC,   SINGULAR, UINT32,   container,         7) \
X(a, STATIC,   SINGULAR, UINT32,   unload_no,         8)
#define auto_exchange_ext_manual_task_msg_CALLBACK NULL
#define auto_exchange_ext_manual_task_msg_DEFAULT NULL

extern const pb_msgdesc_t dev_axis_work_state_msg;
extern const pb_msgdesc_t dev_motor_work_state_msg;
extern const pb_msgdesc_t dev_hook_work_state_msg;
extern const pb_msgdesc_t auto_exchange_dev_state_msg;
extern const pb_msgdesc_t auto_exchange_agent_state_msg;
extern const pb_msgdesc_t auto_exchange_task_move_msg;
extern const pb_msgdesc_t auto_exchange_task_grab_msg;
extern const pb_msgdesc_t auto_exchange_task_grab_move_msg;
extern const pb_msgdesc_t auto_exchange_task_grab_integration_msg;
extern const pb_msgdesc_t auto_exchange_dev_cmd_msg;
extern const pb_msgdesc_t auto_exchange_mileage_info_msg;
extern const pb_msgdesc_t auto_exchange_task_msg;
extern const pb_msgdesc_t auto_exchange_task_state_msg;
extern const pb_msgdesc_t workstation_ext_state_msg;
extern const pb_msgdesc_t auto_exchange_ext_state_single_msg;
extern const pb_msgdesc_t auto_exchange_ext_state_multi_msg;
extern const pb_msgdesc_t auto_exchange_ext_task_state_msg_msg;
extern const pb_msgdesc_t auto_exchange_ext_task_msg_msg;
extern const pb_msgdesc_t auto_exchange_ext_manual_task_msg_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define dev_axis_work_state_fields &dev_axis_work_state_msg
#define dev_motor_work_state_fields &dev_motor_work_state_msg
#define dev_hook_work_state_fields &dev_hook_work_state_msg
#define auto_exchange_dev_state_fields &auto_exchange_dev_state_msg
#define auto_exchange_agent_state_fields &auto_exchange_agent_state_msg
#define auto_exchange_task_move_fields &auto_exchange_task_move_msg
#define auto_exchange_task_grab_fields &auto_exchange_task_grab_msg
#define auto_exchange_task_grab_move_fields &auto_exchange_task_grab_move_msg
#define auto_exchange_task_grab_integration_fields &auto_exchange_task_grab_integration_msg
#define auto_exchange_dev_cmd_fields &auto_exchange_dev_cmd_msg
#define auto_exchange_mileage_info_fields &auto_exchange_mileage_info_msg
#define auto_exchange_task_fields &auto_exchange_task_msg
#define auto_exchange_task_state_fields &auto_exchange_task_state_msg
#define workstation_ext_state_fields &workstation_ext_state_msg
#define auto_exchange_ext_state_single_fields &auto_exchange_ext_state_single_msg
#define auto_exchange_ext_state_multi_fields &auto_exchange_ext_state_multi_msg
#define auto_exchange_ext_task_state_msg_fields &auto_exchange_ext_task_state_msg_msg
#define auto_exchange_ext_task_msg_fields &auto_exchange_ext_task_msg_msg
#define auto_exchange_ext_manual_task_msg_fields &auto_exchange_ext_manual_task_msg_msg

/* Maximum encoded size of messages (where known) */
#define AUTO_EXCHANGE_PB_H_MAX_SIZE              auto_exchange_ext_state_multi_size
#define auto_exchange_agent_state_size           14
#define auto_exchange_dev_cmd_size               8
#define auto_exchange_dev_state_size             516
#define auto_exchange_ext_manual_task_msg_size   116
#define auto_exchange_ext_state_multi_size       4310
#define auto_exchange_ext_state_single_size      428
#define auto_exchange_ext_task_msg_size          102
#define auto_exchange_ext_task_state_msg_size    108
#define auto_exchange_mileage_info_size          22
#define auto_exchange_task_grab_integration_size 159
#define auto_exchange_task_grab_move_size        134
#define auto_exchange_task_grab_size             57
#define auto_exchange_task_move_size             37
#define auto_exchange_task_size                  180
#define auto_exchange_task_state_size            16
#define dev_axis_work_state_size                 66
#define dev_hook_work_state_size                 44
#define dev_motor_work_state_size                33
#define workstation_ext_state_size               16

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

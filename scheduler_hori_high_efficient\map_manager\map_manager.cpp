
/**@file  	   device_manage.cpp
* @brief       本播墙调度系统的设备管理接口文件
* @details     NULL
* <AUTHOR>
* @date        2022-02-14
* @version     v1.0.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.1.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2021/06/25  <td>1.0.0    <td>lizhy     <td>初始版本	                   </tr>
* </table>
*
**********************************************************************************
*/

#include "map_manager.hpp"

#include "share/global_def.h"

#include "share/nlohmann_json/json.hpp"


#include <spdlog/spdlog.h>
#include <spdlog/sinks/daily_file_sink.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/sinks/vehicle_file_sink.h>
#include <spdlog/sinks/stdout_sinks.h>
#include <spdlog/logger.h>    
#include <spdlog/async.h>

#include "share/pb/idl/exception.pb.h"

#include "share/pb/idl/data_map.pb.h"
#include "share/pb/idl/data_request.pb.h"
#include "share/pb/idl/train_interface.pb.h"

#include <zmq.h>
#include <cppzmq/zmq.hpp>
#include <cppzmq/zmq_addon.hpp>

#include <sys/types.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <netdb.h>

#include <fcntl.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <errno.h>
#include <iostream>
#include <stdexcept>
#include <memory>
#include <mutex>          
#include <fstream>


#include "../setting/setting.hpp"



using namespace std;


/**@brief  vehicle_list_map class析构函数
* @param[in]  NULL
* @return     NULL
*/
map_manager::~map_manager() 
{
	//m_vehicle_dev_list.clear();
	//m_vehicle_sock_list.clear();
}


/**@brief     scheduler_manager 初始化函数，对使用到的ZMQ socket进行初始化
* @param[in]  NULL
* @return     函数执行结果
* - true      server创建成功
*/
bool map_manager::map_manager_init(zmq::context_t &ctx) 
{
	
	if( 0>map_manager_get_sys_map() )
	{
		//加载内部地图信息报错

	}
	std::this_thread::sleep_for(std::chrono::milliseconds(500));
	m_global_route = m_dev_map.total_length;

	if( 0>map_manager_get_train_length() )
	{
		//加载内部地图信息报错

	}
	std::this_thread::sleep_for(std::chrono::milliseconds(500));
	if( 0>map_manager_get_coord_conv_para() )
	{
		//加载内部地图信息报错

	}
	
	std::this_thread::sleep_for(std::chrono::milliseconds(500));


	m_following_dis = setting::get_instance()->setting_get_sys_dev_following_distance();
	m_apply_max_len = setting::get_instance()->setting_get_sys_dev_apply_max_len();
	m_apply_max_len_calib = setting::get_instance()->setting_get_sys_dev_apply_max_len_calib();

	SPDLOG_INFO(" m_following_dis :{}  m_apply_max_len :{}  m_apply_max_len_calib :{} ", m_following_dis, m_apply_max_len, m_apply_max_len_calib);
	 
	return true;
}

void map_manager::map_manager_run() 
{
	//m_dev_map_path_unlock_thread = new std::thread(&map_manager::map_manager_vertical_path_unlock_monitor, this);

}


int map_manager::map_manager_get_sys_map(void) 
{
	zmq::context_t context{1};

	uint8_t req_msg[32];
	pb_ostream_t stream_out;
	data_request request;
	int timeout = 1000;

	zmq::socket_t socket{context, zmq::socket_type::req};
    socket.connect(SERVICE_DATA_ACCESS);

	zmq_setsockopt (socket, ZMQ_RCVTIMEO, &timeout, sizeof(timeout));
	zmq_setsockopt (socket, ZMQ_SNDTIMEO, &timeout, sizeof(timeout));

	strncpy(request.key, DATA_KEY_MAP, sizeof(request.key));
	request.type = data_request_cmd_READ;
	stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
	if (!pb_encode(&stream_out, data_request_fields, &request))
	{
		SPDLOG_DEBUG("pb encode error: {}", stream_out.errmsg);
		return -1;
	}
	else
		socket.send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

	zmq::message_t reply;
	pb_istream_t stream_in;
	socket.recv(reply, zmq::recv_flags::none);
	stream_in = pb_istream_from_buffer((const uint8_t *)reply.data(), reply.size());
	if (!pb_decode(&stream_in, data_map_fields, &m_dev_map))
	{
		SPDLOG_DEBUG("pb decode error: {}", stream_in.errmsg);
		return -1;
	}
    
	socket.disconnect(SERVICE_DATA_ACCESS);

	SPDLOG_INFO(" map_info.  :{} ", m_dev_map.total_length);

	return 0 ;

}


int map_manager::map_manager_get_train_length(void) 
{
	zmq::context_t context{1};

	uint8_t req_msg[32];
	pb_ostream_t stream_out;
	data_request request;
	int timeout = 1000;

	zmq::socket_t socket{context, zmq::socket_type::req};
    socket.connect(SERVICE_DATA_ACCESS);

	zmq_setsockopt (socket, ZMQ_RCVTIMEO, &timeout, sizeof(timeout));
	zmq_setsockopt (socket, ZMQ_SNDTIMEO, &timeout, sizeof(timeout));


	strncpy(request.key, DATA_KEY_TRAIN_INFO, sizeof(request.key));
	request.type = data_request_cmd_READ;
	stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
	if (!pb_encode(&stream_out, data_request_fields, &request))
	{
		SPDLOG_DEBUG("pb encode error: {}", stream_out.errmsg);
		return -1;
	}
	else
		socket.send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

	zmq::message_t reply;
	pb_istream_t stream_in;
	socket.recv(reply, zmq::recv_flags::none);
	stream_in = pb_istream_from_buffer((const uint8_t *)reply.data(), reply.size());
	if (!pb_decode(&stream_in, train_para_fields, &m_train_para))
	{
		SPDLOG_DEBUG("pb decode error: {}", stream_in.errmsg);
		return -1;
	}
    
	socket.disconnect(SERVICE_DATA_ACCESS);

	SPDLOG_INFO(" para_temp.  :{} ", m_train_para.train_len);

	return 0 ;

}



int map_manager::map_manager_get_coord_conv_para(void) 
{
	zmq::context_t context{1};

	uint8_t req_msg[32];
	pb_ostream_t stream_out;
	data_request request;
	int timeout = 1000;

	zmq::socket_t socket{context, zmq::socket_type::req};
    socket.connect(SERVICE_DATA_ACCESS);

	zmq_setsockopt (socket, ZMQ_RCVTIMEO, &timeout, sizeof(timeout));
	zmq_setsockopt (socket, ZMQ_SNDTIMEO, &timeout, sizeof(timeout));

	strncpy(request.key, DATA_KEY_COORD_CONV, sizeof(request.key));
	request.type = data_request_cmd_READ;
	stream_out = pb_ostream_from_buffer(req_msg, sizeof(req_msg));
	if (!pb_encode(&stream_out, data_request_fields, &request))
	{
		SPDLOG_DEBUG("pb encode error: {}", stream_out.errmsg);
		return -1;
	}
	else
		socket.send(zmq::buffer(req_msg, stream_out.bytes_written), zmq::send_flags::none);

	zmq::message_t reply;
	pb_istream_t stream_in;
	socket.recv(reply, zmq::recv_flags::none);
	stream_in = pb_istream_from_buffer((const uint8_t *)reply.data(), reply.size());
	if (!pb_decode(&stream_in, coordinate_conv_para_fields, &m_coord_conv_para))
	{
		SPDLOG_DEBUG("pb decode error: {}", stream_in.errmsg);
		return -1;
	}
    
	socket.disconnect(SERVICE_DATA_ACCESS);

	SPDLOG_INFO(" m_coord_conv_para.  :{} :{} :{}", m_coord_conv_para.x_axis_base_offset, m_coord_conv_para.y_axis_base_offset, m_coord_conv_para.z_axis_base_offset);

	return 0 ;

}

void map_manager::map_manager_inner_service_thread() 
{


}


void map_manager::map_manager_dev_self_pos_lock(int dev_id, int dev_pos) 
{
	map_pos_lock dev_temp;

	std::lock_guard<std::mutex> lock(m_map_opt_lock);

	dev_temp.dev_id = dev_id;
	dev_temp.start = dev_pos;
	dev_temp.following = m_following_dis;

	dev_temp.end = dev_pos-(m_train_para.train_len+m_following_dis);
	if( 0>dev_temp.end)
	{
		dev_temp.end += m_dev_map.total_length;
		dev_temp.orig_flag = true;
	}
	else
	{
		dev_temp.orig_flag = false;
	}

	

	//检查当前设备是否存在
	if( m_dev_map_lock.find(dev_id) == m_dev_map_lock.end() )
	{
		m_dev_map_lock.insert(std::make_pair(dev_id,dev_temp));
	}
	else
	{
		m_dev_map_lock.at(dev_id) = dev_temp;
	}

	SPDLOG_INFO("dev :{} try lock route :{} :{} :{} :{}", dev_id, dev_pos, dev_temp.start, dev_temp.end, dev_temp.orig_flag);
	
}


bool  map_manager::map_manager_dev_route_check(int dev_id, int dev_pos)
{
	bool result = true;

	std::lock_guard<std::mutex> lock(m_map_opt_lock);

	//int curr_size = m_dev_map_lock.size();

	int start_pos = dev_pos;
	int end_pos = m_train_para.train_len+m_following_dis;

	for (auto it = m_dev_map_lock.begin(); it != m_dev_map_lock.end(); it ++) 
	{
		if( (start_pos<=it->second.start)||(start_pos>=it->second.end) )
		{
			result = false;
			break;
		}

		if( (end_pos<=it->second.start)||(end_pos>=it->second.end) )
		{
			result = false;
			break;
		}

    }

	return result;

}

int  map_manager::map_manager_dev_route_apply(int dev_id, int curr_pos)
{
	//int valid_route_len = 0;
	int valid_temp_old = -1;
	int valid_temp_curr = 0;
	std::lock_guard<std::mutex> lock(m_map_opt_lock);

	for (auto it = m_dev_map_lock.begin(); it != m_dev_map_lock.end(); it ++) 
	{
		SPDLOG_INFO("m_dev_map_lock info :{} :{}  start :{}  end :{} ", it->first, it->second.dev_id, it->second.start, it->second.end);
	}

	for (auto it = m_dev_map_lock.begin(); it != m_dev_map_lock.end(); it ++) 
	{
		SPDLOG_INFO("m_dev_map_lock curr opt dev :{} :{}  start :{}  end :{} , apply dev pos :{}", it->first, it->second.dev_id, it->second.start, it->second.end, curr_pos);
		
		if( dev_id != it->first)
		{
			//若当前车在申请车辆后方，不做处理
			if( curr_pos>(it->second.start+m_train_para.train_len+m_following_dis) )
			{
				SPDLOG_INFO("soft log");
				continue;
			}

			if( (curr_pos<=it->second.start)||(curr_pos>=it->second.end) )
			{
				SPDLOG_INFO("soft log");
				return -1;
			}

			if( curr_pos<=it->second.end )
			{
				valid_temp_curr = it->second.end - curr_pos;
				SPDLOG_INFO("valid route len :{} :{} ", it->first, valid_temp_curr);
			}

			SPDLOG_INFO("valid_temp_old :{}  valid_temp_curr :{} ", valid_temp_old, valid_temp_curr);

			if( -1==valid_temp_old )
			{
				valid_temp_old = valid_temp_curr;
			}

			SPDLOG_INFO("valid_temp_old :{}  valid_temp_curr :{} ", valid_temp_old, valid_temp_curr);

			if( valid_temp_curr>=valid_temp_old )
			{
				valid_temp_old = valid_temp_curr;
			}

			SPDLOG_INFO("valid_temp_old :{}  valid_temp_curr :{} ", valid_temp_old, valid_temp_curr);

		}
		else
		{

			SPDLOG_INFO("soft log");
			auto it_next = it;
			it_next++;
			if( it_next==m_dev_map_lock.end() )
			{
				if( -1==valid_temp_old )
				{
					valid_temp_curr = m_apply_max_len;
				}
				
			}
			else
			{
				continue;
			}
			//continue;

		}

		
    }

	if( valid_temp_curr>m_apply_max_len )
	{
		return m_apply_max_len;
	}
	else
	{
		return valid_temp_curr;
	}

}

int  map_manager::map_manager_dev_route_apply(int dev_id, int expect_limit, int &target_pos, bool &ori_flag)
{
	//int valid_route_len = 0;
	int curr_route_head = 0;
	int curr_route_tail = 0;

	int valid_temp_old = -1;
	int valid_temp_curr = 0;
	int valid_rout_len_final = 0;
	int temp_target_pos = -1;
	std::lock_guard<std::mutex> lock(m_map_opt_lock);

	for (auto it = m_dev_map_lock.begin(); it != m_dev_map_lock.end(); it ++) 
	{
		SPDLOG_INFO("m_dev_map_lock info :{} :{}  start :{}  end :{} ", it->first, it->second.dev_id, it->second.start, it->second.end);
	}

	curr_route_head = m_dev_map_lock.at(dev_id).start;
	curr_route_tail = m_dev_map_lock.at(dev_id).end;

	SPDLOG_INFO("dev :{} curr lock rout head :{}", dev_id, curr_route_head, curr_route_tail);

	for (auto it = m_dev_map_lock.begin(); it != m_dev_map_lock.end(); it ++) 
	{
		SPDLOG_INFO("m_dev_map_lock curr opt dev :{} :{}  start :{}  end :{} ", it->first, it->second.dev_id, it->second.start, it->second.end);

		if( dev_id != it->first)
		{
			//计算当前车和其他车之间的位置相关性

			//当申请车辆尾部还在当前遍历车辆的前方时，无需处理
			if( curr_route_tail>(it->second.start+m_following_dis) )
			{
				SPDLOG_INFO("soft log");
				//continue;
			}

			//当申请车锁住的位置在另一台车中间时
			if( (curr_route_head<=it->second.start)&&(curr_route_head>=it->second.end) )
			{
				SPDLOG_INFO("soft log");
				return -1;
			}

			if( (curr_route_tail<=it->second.start)&&(curr_route_tail>=it->second.end) )
			{
				SPDLOG_INFO("soft log");
				return -1;
			}

			if( curr_route_head<it->second.end )
			{
				valid_temp_curr = it->second.end - curr_route_head;
				SPDLOG_INFO("valid route len :{} :{} ", it->first, valid_temp_curr);
			}
			else if( curr_route_tail>it->second.start )
			{
				valid_temp_curr = (it->second.end+m_dev_map.total_length) - curr_route_head;
				SPDLOG_INFO("valid route len :{} :{} ", it->first, valid_temp_curr);
			}

			SPDLOG_INFO("valid_temp_old :{}  valid_temp_curr :{} ", valid_temp_old, valid_temp_curr);

			if( -1==valid_temp_old )
			{
				valid_temp_old = valid_temp_curr;
			}

			SPDLOG_INFO("valid_temp_old :{}  valid_temp_curr :{} ", valid_temp_old, valid_temp_curr);

			if( valid_temp_curr<valid_temp_old )
			{
				valid_temp_old = valid_temp_curr;
			}

			SPDLOG_INFO("valid_temp_old :{}  valid_temp_curr :{} ", valid_temp_old, valid_temp_curr);

		}
		else
		{
			SPDLOG_INFO("soft log");

			if( 1==m_dev_map_lock.size() )
			{
				//系统内只有这一台车
				valid_temp_curr = m_apply_max_len;
			}
			else
			{
				continue;
#if 0
				auto it_next = it;
				it_next++;
				if( it_next==m_dev_map_lock.end() )
				{
					if( -1==valid_temp_old )
					{
						valid_temp_curr = m_apply_max_len;
					}
				
				}
				else
				{
					valid_temp_old = -1;
					valid_temp_curr = m_apply_max_len;
					continue;
				}
#endif

			}
		
		}

	}




	if( valid_temp_curr>m_apply_max_len )
	{
		valid_rout_len_final = m_apply_max_len;
	}
	else
	{
		valid_rout_len_final = valid_temp_curr;
	}


	if( valid_rout_len_final > expect_limit)
	{
		valid_rout_len_final = expect_limit;
	}

	if( 0==valid_rout_len_final )
	{
		return 0;
	}

	temp_target_pos = curr_route_head+valid_rout_len_final;


	if( temp_target_pos>m_dev_map.total_length )
	{
		target_pos = (temp_target_pos-m_dev_map.total_length);
		ori_flag = true;
	}
	else
	{
		target_pos = temp_target_pos;
		ori_flag = false;
	}

	return valid_rout_len_final;

}




int  map_manager::map_manager_get_sys_feeder_cnt(void)
{
	return m_dev_map.feeders.feeder_info_count;
}


bool  map_manager::map_manager_get_sys_feeder_info(int id, data_map_feeders_infomation &info)
{
	bool result = false;
	int i;
	data_map_feeders_infomation temp;

	for(i=0; i<m_dev_map.feeders.feeder_info_count; i++)
	{
		if( id==(int)m_dev_map.feeders.feeder_info[i].feeder_id )
		{
			temp = m_dev_map.feeders.feeder_info[i];
			result = true;
			break;
		}

	}

	info = temp;

	return result;
}



int map_manager::map_manager_get_feeder_base_camera_id(int camera_id)
{
	//bool result = false;
	int i;
	int feeder_id = -1;
	data_map_feeders_infomation temp;

	for(i=0; i<m_dev_map.feeders.feeder_info_count; i++)
	{
		if( camera_id==(int)m_dev_map.feeders.feeder_info[i].bind_gray_camera )
		{
			temp = m_dev_map.feeders.feeder_info[i];
			feeder_id = m_dev_map.feeders.feeder_info[i].feeder_id;
			break;
		}

	}

	return feeder_id;
}



int map_manager::map_manager_get_sys_feeder_heigth(int id)
{
	int heigth = -1;
	int i;
	//data_map_feeders_infomation temp;

	for(i=0; i<m_dev_map.feeders.feeder_info_count; i++)
	{
		if( id==(int)m_dev_map.feeders.feeder_info[i].feeder_id )
		{
			heigth = m_dev_map.feeders.feeder_info[i].height;
			break;
		}

	}

	return heigth;
}

int map_manager::map_manager_get_sys_feeder_heigth(void)
{
	int height = -1;
	//int i;
	//data_map_feeders_infomation temp;

	if( m_dev_map.feeders.feeder_info_count<1 )
	{
		//return -1;
		return m_dev_map.feeders.feeder_info[0].height;
	}
	else
	{
		height = m_dev_map.feeders.feeder_info[0].height;
	}

	return height;

}



bool  map_manager::map_manager_get_designated_container_info(int id, data_map_container_info &info)
{
	bool result = false;
	int i;
	data_map_container_info temp;

	for(i=0; i<m_dev_map.containers.container_count; i++)
	{
		if( id==(int)m_dev_map.containers.container[i].id )
		{
			temp = m_dev_map.containers.container[i];
			result = true;
			break;
		}

	}

	info = temp;

	return result;
}



int  map_manager::map_manager_get_top_container_height(void)
{
	int height_temp  = 0;
	int i=0;

	for(i=0; i<m_dev_map.containers.container_count; i++)
	{
		if( height_temp<=m_dev_map.containers.container[i].height )
		{
			height_temp = m_dev_map.containers.container[i].height;
		}
	}

	SPDLOG_INFO(" height_temp :{} ", height_temp);

	return height_temp;
}


int  map_manager::map_manager_get_dev_total_height(void)
{
	int height_temp  = m_dev_map.tunnel_height;

	SPDLOG_INFO(" height_temp :{} ", height_temp);

	return height_temp;
}


bool  map_manager::map_manager_get_designated_camera_info(int id, data_map_gray_camera_info &info)
{
	bool result = false;
	int i;
	data_map_gray_camera_info temp;

	for(i=0; i<m_dev_map.gray_camera_count; i++)
	{
		if( id==(int)m_dev_map.gray_camera[i].id )
		{
			temp = m_dev_map.gray_camera[i];
			result = true;
			break;
		}

	}

	info = temp;

	return result;
}

bool  map_manager::map_manager_is_curr_pos_camera_confirm_valid(int pos)
{
	bool result = false;
	int i;
	data_map_gray_camera_info temp;

	for(i=0; i<m_dev_map.feeders.feeder_info_count; i++)
	{
		map_manager_get_designated_camera_info(m_dev_map.feeders.feeder_info[i].bind_gray_camera , temp);
		
		//SPDLOG_INFO("pos :{} feeder :{} :{} camera :{} :{}", pos, m_dev_map.feeders.feeder_info[i].feeder_id, m_dev_map.feeders.feeder_info[i].position,
		//temp.id, temp.dev_pos);

		//if( pos<(temp.dev_pos+m_train_para.carriage_len)&&(pos>(m_dev_map.feeders.feeder_info[i].position-m_train_para.carriage_len)) )
		//if( pos<(temp.dev_pos+m_train_para.carriage_len)&&(pos>(m_dev_map.feeders.feeder_info[i].position-(m_train_para.carriage_len/2))) )
		if( pos<(temp.dev_pos+(m_train_para.carriage_len/2))&&(pos>(m_dev_map.feeders.feeder_info[i].position-m_train_para.carriage_len)) )
		{
			result = true;
			break;
		}
	}

	return result;



	//for(i=0; i<m_dev_map.gray_camera_count; i++)
	//{
		//if( (pos<(m_dev_map.gray_camera[i].dev_pos+m_train_para.carriage_len))&&( abs(pos-m_dev_map.gray_camera[i].dev_pos)<(500*2) ) )
		//{
		//	result = true;
		//	break;
		//}
		
		//if( pos<(m_dev_map.gray_camera[i].dev_pos+m_train_para.carriage_len) )
		//{
		//	result = true;
		//}
	//}

	

}



bool  map_manager::map_manager_is_curr_pos_feeder_protect(int pos)
{
	bool result = true;
	int i;
	//data_map_gray_camera_info temp;

	for(i=0; i<m_dev_map.feeders.feeder_info_count; i++)
	{
		if( (pos>(m_dev_map.feeders.feeder_info[i].position-(m_dev_map.feeders.feeder_info[i].feeder_width/2)-m_train_para.carriage_len))&&(pos<(m_dev_map.feeders.feeder_info[i].position+m_dev_map.feeders.feeder_info[i].feeder_width+m_train_para.carriage_len)) )
		{
			result = false;
			break;
		}		
	}

	return result;

}


data_map_containers_info map_manager::map_manager_get_sys_container_info(void)
{
	return m_dev_map.containers;
}


train_para map_manager::map_manager_get_sys_train_info(void)
{
	return m_train_para;
}




bool map_manager::map_manager_find_nearest_feeder(int des, int &feeder_id, int &distance)
{
	bool result = false;
	int i;
	int destance_temp = -1;
	int destance_old = -1;
	int id_temp = -1;

	for(i=0; i<m_dev_map.feeders.feeder_info_count; i++)
	{
		if( des<m_dev_map.feeders.feeder_info[i].position )
		{
			destance_temp = m_dev_map.feeders.feeder_info[i].position-des;
		}
		else
		{
			destance_temp = m_dev_map.feeders.feeder_info[i].position-des+m_dev_map.total_length;
		}

		
		if( destance_old<0 )
		{
			destance_old = destance_temp;
			id_temp = m_dev_map.feeders.feeder_info[i].feeder_id;
		}

		if( destance_old<=destance_temp )
		{
			//do nothing
		}
		else
		{
			destance_old = destance_temp;
			id_temp = m_dev_map.feeders.feeder_info[i].feeder_id;
		}

	}	

	if( destance_old<0 )
	{
		result = false;
	}
	else
	{
		result = true;
	}

	feeder_id = id_temp;
	distance = destance_old;
	
	SPDLOG_INFO("find feeder target :{} :{} :{} :{}", result, des, id_temp, destance_old);


	return result;
}

bool map_manager::map_manager_if_device_in_feeder_range(int pos, int &feeder_id)
{
	bool result = false;
	int i;
	//int destance_temp = -1;
	//int destance_old = -1;
	int id_temp = -1;

	for(i=0; i<m_dev_map.feeders.feeder_info_count; i++)
	{
		if( (pos>(m_dev_map.feeders.feeder_info[i].position-m_train_para.carriage_len))&&(pos<=(m_dev_map.feeders.feeder_info[i].position+(m_dev_map.feeders.feeder_info[i].feeder_width/2)+m_train_para.carriage_len)) )
		{
			result = true;
			id_temp = m_dev_map.feeders.feeder_info[i].feeder_id;
			break;
		}

	}


	feeder_id = id_temp;

	SPDLOG_INFO("soft log :{} :{} :{} :{} :{} :{} ", pos, (m_dev_map.feeders.feeder_info[i].position-m_train_para.carriage_len), 
	(m_dev_map.feeders.feeder_info[i].position+(m_dev_map.feeders.feeder_info[i].feeder_width/2)+m_train_para.carriage_len), id_temp, feeder_id, result);
	
	return result;
}


int map_manager::map_manager_fine_nearest_hos_container(int dev_pos )
{
	int hos_slot_id = -1;
	int i;
	int curr_destance = -1;
	int old_destance = -1;

	for(i=0; i<m_dev_map.containers.container_count; i++)
	{
		if( data_map_container_type_HOSPICE == m_dev_map.containers.container[i].type )
		{
			if( m_dev_map.containers.container[i].position>dev_pos )
			{
				curr_destance = m_dev_map.containers.container[i].position-dev_pos;
			}
			else
			{
				curr_destance = m_dev_map.containers.container[i].position-dev_pos+m_dev_map.total_length;
			}


			if( old_destance<0 )
			{
				old_destance = curr_destance;
				hos_slot_id = m_dev_map.containers.container[i].id;
			}

			if( curr_destance<old_destance )
			{
				old_destance = curr_destance;
				hos_slot_id = m_dev_map.containers.container[i].id;
			}

		}
	}


	return hos_slot_id;
}





int map_manager::map_manager_get_feeder_bind_hos_slot(int id)
{
	int result = -1;
	int i;

	for(i=0; i<m_dev_map.feeders.feeder_info_count; i++)
	{
		if( id==(int)m_dev_map.feeders.feeder_info[i].feeder_id )
		{
			result = m_dev_map.feeders.feeder_info[i].bind_hospice_cont;
			break;
		}

	}

	return result;
}


float map_manager::map_manager_get_sys_straight_len(void)
{
	return m_dev_map.tunnel_straight;
}

float map_manager::map_manager_get_sys_arc_len(void)
{
	return m_dev_map.arc_r*3.1415926;
}



float map_manager::map_manager_get_dev_total_len(void)
{
	return m_dev_map.total_length;
}



bool  map_manager::map_manager_is_curr_pos_feeder_avoid_valid(int pos)
{
	bool result = false;
	int i;

	//直线段直接返回false
	if( (pos>=0)&&(pos<=m_dev_map.tunnel_straight)  )
	{
		SPDLOG_INFO("llllllllllllllllllog");
		result = false;
		//result = true;
		return result;
	}

	if( (pos>=(m_dev_map.tunnel_straight+3.14159*m_dev_map.arc_r))&&(pos<=(m_dev_map.tunnel_straight*2+3.14159*m_dev_map.arc_r))  )
	{
		SPDLOG_INFO("llllllllllllllllllog");
		result = false;
		//result = true;
		return result;
	}


#if 0
	for(i=0; i<m_dev_map.gray_camera_count; i++)
	{
		SPDLOG_INFO("pos :{} :{} :{} :{} :{}", pos, m_dev_map.gray_camera[i].id, m_dev_map.gray_camera[i].dev_pos, 
		m_train_para.carriage_len, m_dev_map.gray_camera[i].dev_pos+m_train_para.carriage_len);

		if( (pos>(m_dev_map.gray_camera[i].dev_pos+m_train_para.carriage_len)) )
		{
			SPDLOG_INFO("llllllllllllllllllog");
			result = true;
			break;
		}	
	}
#endif

	//feeder 1弧线段
	if( (pos>m_dev_map.tunnel_straight)&&(pos<(m_dev_map.tunnel_straight+3.14159*m_dev_map.arc_r)) )
	{
		for(i=0; i<m_dev_map.gray_camera_count; i++)
		{
			SPDLOG_INFO("pos :{} :{} :{} :{} :{}", pos, m_dev_map.gray_camera[i].id, m_dev_map.gray_camera[i].dev_pos, 
			m_train_para.carriage_len, m_dev_map.gray_camera[i].dev_pos+m_train_para.carriage_len);

			if( (pos>(m_dev_map.gray_camera[i].dev_pos+(m_train_para.carriage_len/2))) )
			{
				SPDLOG_INFO("llllllllllllllllllog");
				result = true;
				break;
			}	
		}
	}
	else if( pos>=(m_dev_map.tunnel_straight*2+3.14159*m_dev_map.arc_r) ) 
	{
		result = true;

		for(i=0; i<m_dev_map.gray_camera_count; i++)
		{
			SPDLOG_INFO("pos :{} :{} :{} :{} :{}", pos, m_dev_map.gray_camera[i].id, m_dev_map.gray_camera[i].dev_pos, 
			m_train_para.carriage_len, m_dev_map.gray_camera[i].dev_pos+m_train_para.carriage_len);

			if( (pos<(m_dev_map.gray_camera[i].dev_pos+(m_train_para.carriage_len/2))) )
			{
				SPDLOG_INFO("llllllllllllllllllog");
				result = false;
				break;
			}	
		}


	}


	SPDLOG_INFO("llllllllllllllllllog");

	return result;


}



#define  PI  (3.1415926)

position_xyz map_manager::map_manager_coord_conv(int mileage, int y_axis)
{
	position_xyz pos_temp;
	float temp_len;
	double x0;
	double y0;
	double x1;
	double y1;

	if( (mileage>=0)&&(mileage<m_dev_map.tunnel_straight) )
	{
		pos_temp.x = mileage+m_coord_conv_para.x_axis_base_offset;
		pos_temp.y = m_coord_conv_para.y_axis_base_offset;
	}
	else if( (mileage>=m_dev_map.tunnel_straight)&&(mileage<(m_dev_map.tunnel_straight+3.14159*m_dev_map.arc_r)) )
	{
		temp_len = mileage-m_dev_map.tunnel_straight;
		x0 = m_dev_map.tunnel_straight+m_coord_conv_para.x_axis_base_offset;
		y0 = m_dev_map.arc_r+m_coord_conv_para.y_axis_base_offset;
		//x1 = x0+m_dev_map.arc_r*cos( 3.14159*3.14159*m_dev_map.arc_r/(180*180*temp_len) );
		//y1 = y0+m_dev_map.arc_r*sin( 3.14159*3.14159*m_dev_map.arc_r/(180*180*temp_len) );
		x1 = x0+m_dev_map.arc_r*cos( (temp_len/m_dev_map.arc_r/PI)*180 - 90 );
		y1 = y0+m_dev_map.arc_r*sin( (temp_len/m_dev_map.arc_r/PI)*180 - 90 );
		pos_temp.x = (int)x1;
		pos_temp.y = (int)y1;
	}
	else if( (mileage>=(m_dev_map.tunnel_straight+3.14159*m_dev_map.arc_r))&&(mileage<(m_dev_map.tunnel_straight*2+3.14159*m_dev_map.arc_r)) )
	{
		pos_temp.x = m_dev_map.tunnel_straight*2+3.14159*m_dev_map.arc_r-mileage+m_coord_conv_para.x_axis_base_offset;
		pos_temp.y = m_coord_conv_para.y_axis_base_offset+m_dev_map.arc_r*2;
	}
	else if( (mileage>=(m_dev_map.tunnel_straight*2+3.14159*m_dev_map.arc_r))&&(mileage<(m_dev_map.total_length)) )
	{
		temp_len = mileage-(m_dev_map.tunnel_straight*2+3.14159*m_dev_map.arc_r);
		x0 = m_coord_conv_para.x_axis_base_offset;
		y0 = m_dev_map.arc_r+m_coord_conv_para.y_axis_base_offset;
		//x1 = x0-m_dev_map.arc_r*cos( 3.14159*3.14159*m_dev_map.arc_r/(180*180*temp_len) );
		//y1 = y0+m_dev_map.arc_r*sin( 3.14159*3.14159*m_dev_map.arc_r/(180*180*temp_len) );
		x1 = x0+m_dev_map.arc_r*cos( (temp_len/m_dev_map.arc_r/PI)*180 + 90 );
		y1 = y0+m_dev_map.arc_r*sin( (temp_len/m_dev_map.arc_r/PI)*180 + 90 );
		pos_temp.x = (int)x1;
		pos_temp.y = (int)y1;
		SPDLOG_INFO("temp_len :{} :{} :{}", temp_len, x1, y1);
	}

	pos_temp.z = y_axis+m_coord_conv_para.z_axis_base_offset;

	SPDLOG_INFO("input :{} :{}", mileage, y_axis);
	SPDLOG_INFO("output :{} :{} :{}", pos_temp.x, pos_temp.y, pos_temp.z);

	return pos_temp;
} 




data_map_tunnel_type map_manager::map_manager_curr_pos_type(int pos)
{
	data_map_tunnel_type type_temp = data_map_tunnel_type_TUNNEL_TYPE_RESERVE;

	if( (pos>=0)&&(pos<m_dev_map.tunnel_straight) )
	{
		type_temp = data_map_tunnel_type_TUNNEL_TYPE_STRAIGHT;
	}
	else if( (pos>=m_dev_map.tunnel_straight)&&(pos<(m_dev_map.tunnel_straight+3.14159*m_dev_map.arc_r)) )
	{
		type_temp = data_map_tunnel_type_TUNNEL_TYPE_QRC;
	}
	else if( (pos>=(m_dev_map.tunnel_straight+3.14159*m_dev_map.arc_r))&&(pos<(m_dev_map.tunnel_straight*2+3.14159*m_dev_map.arc_r)) )
	{
		type_temp = data_map_tunnel_type_TUNNEL_TYPE_STRAIGHT;
	}
	else if( (pos>=(m_dev_map.tunnel_straight*2+3.14159*m_dev_map.arc_r))&&(pos<(m_dev_map.total_length)) )
	{
		type_temp = data_map_tunnel_type_TUNNEL_TYPE_QRC;
	}
	else
	{
		type_temp = data_map_tunnel_type_TUNNEL_TYPE_RESERVE;
	}

	return type_temp;
} 



int map_manager::map_manager_curr_pos_arc_distance(int pos)
{
	int dis_temp = 0;

	if( (pos>=0)&&(pos<m_dev_map.tunnel_straight) )
	{
		return 0;
	}
	else if( (pos>=m_dev_map.tunnel_straight)&&(pos<(m_dev_map.tunnel_straight+3.14159*m_dev_map.arc_r)) )
	{
		dis_temp = (int)(m_dev_map.tunnel_straight+3.14159*m_dev_map.arc_r-pos);
	}
	else if( (pos>=(m_dev_map.tunnel_straight+3.14159*m_dev_map.arc_r))&&(pos<(m_dev_map.tunnel_straight*2+3.14159*m_dev_map.arc_r)) )
	{
		return 0;
	}
	else if( (pos>=(m_dev_map.tunnel_straight*2+3.14159*m_dev_map.arc_r))&&(pos<(m_dev_map.total_length)) )
	{
		dis_temp = (int)(m_dev_map.tunnel_straight*2+3.14159*m_dev_map.arc_r-pos);
	}
	else
	{
		return 0;
	}

	SPDLOG_INFO("dis_temp :{} ", dis_temp);
	return dis_temp;
} 



int map_manager::map_manager_curr_pos_nearest_arc_distance(int pos)
{
	int dis_temp = 0;

	if( (pos>=0)&&(pos<m_dev_map.tunnel_straight) )
	{
		dis_temp = m_dev_map.tunnel_straight - pos;
	}
	else if( (pos>=m_dev_map.tunnel_straight)&&(pos<(m_dev_map.tunnel_straight+3.14159*m_dev_map.arc_r)) )
	{
		return -1;
	}
	else if( (pos>=(m_dev_map.tunnel_straight+3.14159*m_dev_map.arc_r))&&(pos<(m_dev_map.tunnel_straight*2+3.14159*m_dev_map.arc_r)) )
	{
		dis_temp = (m_dev_map.tunnel_straight*2+3.14159*m_dev_map.arc_r) - pos;
	}
	else if( (pos>=(m_dev_map.tunnel_straight*2+3.14159*m_dev_map.arc_r))&&(pos<(m_dev_map.total_length)) )
	{
		return -1;
	}
	else
	{
		return -1;
	}

	SPDLOG_INFO("dis_temp :{} ", dis_temp);
	return dis_temp;
} 


int map_manager::map_manager_curr_pos_nearest_arc_pos(int pos)
{
	int dis_temp = 0;

	if( (pos>=0)&&(pos<m_dev_map.tunnel_straight) )
	{
		dis_temp = m_dev_map.tunnel_straight;
	}
	else if( (pos>=m_dev_map.tunnel_straight)&&(pos<(m_dev_map.tunnel_straight+3.14159*m_dev_map.arc_r)) )
	{
		return -1;
	}
	else if( (pos>=(m_dev_map.tunnel_straight+3.14159*m_dev_map.arc_r))&&(pos<(m_dev_map.tunnel_straight*2+3.14159*m_dev_map.arc_r)) )
	{
		dis_temp = (m_dev_map.tunnel_straight*2+3.14159*m_dev_map.arc_r);
	}
	else if( (pos>=(m_dev_map.tunnel_straight*2+3.14159*m_dev_map.arc_r))&&(pos<(m_dev_map.total_length)) )
	{
		return -1;
	}
	else
	{
		return -1;
	}

	SPDLOG_INFO("dis_temp :{} ", dis_temp);
	return dis_temp;
} 

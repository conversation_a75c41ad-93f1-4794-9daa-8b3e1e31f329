# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/build

# Include any dependencies generated for this target.
include CMakeFiles/compete_manage_example.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/compete_manage_example.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/compete_manage_example.dir/flags.make

CMakeFiles/compete_manage_example.dir/example.cpp.o: CMakeFiles/compete_manage_example.dir/flags.make
CMakeFiles/compete_manage_example.dir/example.cpp.o: ../example.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/compete_manage_example.dir/example.cpp.o"
	/usr/bin/c++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/compete_manage_example.dir/example.cpp.o -c /media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/example.cpp

CMakeFiles/compete_manage_example.dir/example.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/compete_manage_example.dir/example.cpp.i"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/example.cpp > CMakeFiles/compete_manage_example.dir/example.cpp.i

CMakeFiles/compete_manage_example.dir/example.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/compete_manage_example.dir/example.cpp.s"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/example.cpp -o CMakeFiles/compete_manage_example.dir/example.cpp.s

CMakeFiles/compete_manage_example.dir/example.cpp.o.requires:

.PHONY : CMakeFiles/compete_manage_example.dir/example.cpp.o.requires

CMakeFiles/compete_manage_example.dir/example.cpp.o.provides: CMakeFiles/compete_manage_example.dir/example.cpp.o.requires
	$(MAKE) -f CMakeFiles/compete_manage_example.dir/build.make CMakeFiles/compete_manage_example.dir/example.cpp.o.provides.build
.PHONY : CMakeFiles/compete_manage_example.dir/example.cpp.o.provides

CMakeFiles/compete_manage_example.dir/example.cpp.o.provides.build: CMakeFiles/compete_manage_example.dir/example.cpp.o


CMakeFiles/compete_manage_example.dir/compete_manage.cpp.o: CMakeFiles/compete_manage_example.dir/flags.make
CMakeFiles/compete_manage_example.dir/compete_manage.cpp.o: ../compete_manage.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/compete_manage_example.dir/compete_manage.cpp.o"
	/usr/bin/c++   $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/compete_manage_example.dir/compete_manage.cpp.o -c /media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/compete_manage.cpp

CMakeFiles/compete_manage_example.dir/compete_manage.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/compete_manage_example.dir/compete_manage.cpp.i"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/compete_manage.cpp > CMakeFiles/compete_manage_example.dir/compete_manage.cpp.i

CMakeFiles/compete_manage_example.dir/compete_manage.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/compete_manage_example.dir/compete_manage.cpp.s"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/compete_manage.cpp -o CMakeFiles/compete_manage_example.dir/compete_manage.cpp.s

CMakeFiles/compete_manage_example.dir/compete_manage.cpp.o.requires:

.PHONY : CMakeFiles/compete_manage_example.dir/compete_manage.cpp.o.requires

CMakeFiles/compete_manage_example.dir/compete_manage.cpp.o.provides: CMakeFiles/compete_manage_example.dir/compete_manage.cpp.o.requires
	$(MAKE) -f CMakeFiles/compete_manage_example.dir/build.make CMakeFiles/compete_manage_example.dir/compete_manage.cpp.o.provides.build
.PHONY : CMakeFiles/compete_manage_example.dir/compete_manage.cpp.o.provides

CMakeFiles/compete_manage_example.dir/compete_manage.cpp.o.provides.build: CMakeFiles/compete_manage_example.dir/compete_manage.cpp.o


# Object files for target compete_manage_example
compete_manage_example_OBJECTS = \
"CMakeFiles/compete_manage_example.dir/example.cpp.o" \
"CMakeFiles/compete_manage_example.dir/compete_manage.cpp.o"

# External object files for target compete_manage_example
compete_manage_example_EXTERNAL_OBJECTS =

compete_manage_example: CMakeFiles/compete_manage_example.dir/example.cpp.o
compete_manage_example: CMakeFiles/compete_manage_example.dir/compete_manage.cpp.o
compete_manage_example: CMakeFiles/compete_manage_example.dir/build.make
compete_manage_example: CMakeFiles/compete_manage_example.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable compete_manage_example"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/compete_manage_example.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/compete_manage_example.dir/build: compete_manage_example

.PHONY : CMakeFiles/compete_manage_example.dir/build

CMakeFiles/compete_manage_example.dir/requires: CMakeFiles/compete_manage_example.dir/example.cpp.o.requires
CMakeFiles/compete_manage_example.dir/requires: CMakeFiles/compete_manage_example.dir/compete_manage.cpp.o.requires

.PHONY : CMakeFiles/compete_manage_example.dir/requires

CMakeFiles/compete_manage_example.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/compete_manage_example.dir/cmake_clean.cmake
.PHONY : CMakeFiles/compete_manage_example.dir/clean

CMakeFiles/compete_manage_example.dir/depend:
	cd /media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage /media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage /media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/build /media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/build /media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/build/CMakeFiles/compete_manage_example.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/compete_manage_example.dir/depend


/usr/bin/c++    -<PERSON> -Wextra -O2 -pthread   CMakeFiles/compete_manage_example.dir/example.cpp.o CMakeFiles/compete_manage_example.dir/compete_manage.cpp.o  -o compete_manage_example  -L/media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/../../share/libs/x86/lib -lpthread -lspdlog -Wl,-rpath,/media/sf_work/auto_sort_high_efficient/auto_exchange_ctrl/compete_manage/../../share/libs/x86/lib 

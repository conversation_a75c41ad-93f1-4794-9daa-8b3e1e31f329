#!/bin/bash

# Berxel相机测试程序构建脚本
# 版本: v1.0.0
# 日期: 2025-08-26

echo "=== Berxel相机测试程序构建脚本 ==="

# 检查是否在正确的目录
if [ ! -f "CMakeLists.txt" ]; then
    echo "错误: 请在camera_test目录下运行此脚本"
    exit 1
fi

# 创建构建目录
if [ ! -d "build" ]; then
    echo "创建构建目录..."
    mkdir build
fi

cd build

# 清理之前的构建文件（可选）
if [ "$1" = "clean" ]; then
    echo "清理之前的构建文件..."
    rm -rf *
fi

# 配置项目
echo "配置CMake项目..."
cmake .. -DCMAKE_BUILD_TYPE=Release

if [ $? -ne 0 ]; then
    echo "错误: CMake配置失败"
    exit 1
fi

# 编译项目
echo "编译项目..."
make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "错误: 编译失败"
    exit 1
fi

echo "构建成功！"
echo "可执行文件位置: build/camera_test"
echo ""
echo "运行方法:"
echo "  cd build"
echo "  ./camera_test"
echo ""
echo "或者直接运行:"
echo "  ./build/camera_test"
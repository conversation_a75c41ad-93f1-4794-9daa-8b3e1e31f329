#IncludeRegexLine: ^[ 	]*#[ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../../share/libs/x86/include/BerxelHawkContext.h
BerxelHawkPlatform.h
-
BerxelHawkDefines.h
-

../../share/libs/x86/include/BerxelHawkDefines.h
stdint.h
-

../../share/libs/x86/include/BerxelHawkDevice.h
map
-
vector
-
BerxelHawkPlatform.h
-
BerxelHawkDefines.h
-
BerxelHawkFrame.h
-

../../share/libs/x86/include/BerxelHawkFrame.h
BerxelHawkPlatform.h
-
BerxelHawkDefines.h
-

../../share/libs/x86/include/BerxelHawkPlatform.h
stddef.h
-

../../share/libs/x86/include/spdlog/common-inl.h
spdlog/common.h
-

../../share/libs/x86/include/spdlog/common.h
spdlog/tweakme.h
-
spdlog/details/null_mutex.h
-
atomic
-
chrono
-
initializer_list
-
memory
-
exception
-
string
-
type_traits
-
functional
-
spdlog/fmt/fmt.h
-
common-inl.h
../../share/libs/x86/include/spdlog/common-inl.h

../../share/libs/x86/include/spdlog/details/backtracer-inl.h
spdlog/details/backtracer.h
-

../../share/libs/x86/include/spdlog/details/backtracer.h
spdlog/details/log_msg_buffer.h
-
spdlog/details/circular_q.h
-
atomic
-
mutex
-
functional
-
backtracer-inl.h
../../share/libs/x86/include/spdlog/details/backtracer-inl.h

../../share/libs/x86/include/spdlog/details/circular_q.h
vector
-
cassert
-

../../share/libs/x86/include/spdlog/details/console_globals.h
spdlog/details/null_mutex.h
-
mutex
-

../../share/libs/x86/include/spdlog/details/file_helper-inl.h
spdlog/details/file_helper.h
-
spdlog/details/os.h
-
spdlog/common.h
-
cerrno
-
chrono
-
cstdio
-
string
-
thread
-
tuple
-

../../share/libs/x86/include/spdlog/details/file_helper.h
spdlog/common.h
-
tuple
-
file_helper-inl.h
../../share/libs/x86/include/spdlog/details/file_helper-inl.h

../../share/libs/x86/include/spdlog/details/fmt_helper.h
chrono
-
type_traits
-
spdlog/fmt/fmt.h
-
spdlog/common.h
-

../../share/libs/x86/include/spdlog/details/log_msg-inl.h
spdlog/details/log_msg.h
-
spdlog/details/os.h
-

../../share/libs/x86/include/spdlog/details/log_msg.h
spdlog/common.h
-
string
-
log_msg-inl.h
../../share/libs/x86/include/spdlog/details/log_msg-inl.h

../../share/libs/x86/include/spdlog/details/log_msg_buffer-inl.h
spdlog/details/log_msg_buffer.h
-

../../share/libs/x86/include/spdlog/details/log_msg_buffer.h
spdlog/details/log_msg.h
-
log_msg_buffer-inl.h
../../share/libs/x86/include/spdlog/details/log_msg_buffer-inl.h

../../share/libs/x86/include/spdlog/details/null_mutex.h
atomic
-
utility
-

../../share/libs/x86/include/spdlog/details/os-inl.h
spdlog/details/os.h
-
spdlog/common.h
-
algorithm
-
chrono
-
cstdio
-
cstdlib
-
cstring
-
ctime
-
string
-
thread
-
array
-
sys/stat.h
-
sys/types.h
-
io.h
-
process.h
-
spdlog/details/windows_include.h
-
share.h
-
limits
-
direct.h
-
fcntl.h
-
unistd.h
-
sys/syscall.h
-
pthread.h
-
pthread_np.h
-
lwp.h
-
thread.h
-

../../share/libs/x86/include/spdlog/details/os.h
spdlog/common.h
-
ctime
-
os-inl.h
../../share/libs/x86/include/spdlog/details/os-inl.h

../../share/libs/x86/include/spdlog/details/periodic_worker-inl.h
spdlog/details/periodic_worker.h
-

../../share/libs/x86/include/spdlog/details/periodic_worker.h
chrono
-
condition_variable
-
functional
-
mutex
-
thread
-
periodic_worker-inl.h
../../share/libs/x86/include/spdlog/details/periodic_worker-inl.h

../../share/libs/x86/include/spdlog/details/registry-inl.h
spdlog/details/registry.h
-
spdlog/common.h
-
spdlog/details/periodic_worker.h
-
spdlog/logger.h
-
spdlog/pattern_formatter.h
-
spdlog/sinks/wincolor_sink.h
-
spdlog/sinks/ansicolor_sink.h
-
chrono
-
functional
-
memory
-
string
-
unordered_map
-

../../share/libs/x86/include/spdlog/details/registry.h
spdlog/common.h
-
chrono
-
functional
-
memory
-
string
-
unordered_map
-
mutex
-
registry-inl.h
../../share/libs/x86/include/spdlog/details/registry-inl.h

../../share/libs/x86/include/spdlog/details/synchronous_factory.h
registry.h
../../share/libs/x86/include/spdlog/details/registry.h

../../share/libs/x86/include/spdlog/details/windows_include.h
windows.h
-

../../share/libs/x86/include/spdlog/fmt/bundled/core.h
cstdio
-
cstring
-
functional
-
iterator
-
memory
-
string
-
type_traits
-
vector
-
string_view
-
experimental/string_view
-
fmt/core.h
-

../../share/libs/x86/include/spdlog/fmt/bundled/format-inl.h
cassert
-
cctype
-
climits
-
cmath
-
cstdarg
-
cstring
-
cwchar
-
exception
-
locale
-
io.h
-
format.h
../../share/libs/x86/include/spdlog/fmt/bundled/format.h

../../share/libs/x86/include/spdlog/fmt/bundled/format.h
algorithm
-
cerrno
-
cmath
-
cstdint
-
limits
-
memory
-
stdexcept
-
core.h
../../share/libs/x86/include/spdlog/fmt/bundled/core.h
intrin.h
-
fmt/format.h
-
format-inl.h
../../share/libs/x86/include/spdlog/fmt/bundled/format-inl.h

../../share/libs/x86/include/spdlog/fmt/fmt.h
spdlog/fmt/bundled/core.h
-
spdlog/fmt/bundled/format.h
-
fmt/core.h
-
fmt/format.h
-

../../share/libs/x86/include/spdlog/formatter.h
spdlog/fmt/fmt.h
-
spdlog/details/log_msg.h
-

../../share/libs/x86/include/spdlog/logger-inl.h
spdlog/logger.h
-
spdlog/sinks/sink.h
-
spdlog/details/backtracer.h
-
spdlog/pattern_formatter.h
-
cstdio
-

../../share/libs/x86/include/spdlog/logger.h
spdlog/common.h
-
spdlog/details/log_msg.h
-
spdlog/details/backtracer.h
-
spdlog/details/os.h
-
vector
-
logger-inl.h
../../share/libs/x86/include/spdlog/logger-inl.h

../../share/libs/x86/include/spdlog/pattern_formatter-inl.h
spdlog/pattern_formatter.h
-
spdlog/details/fmt_helper.h
-
spdlog/details/log_msg.h
-
spdlog/details/os.h
-
spdlog/fmt/fmt.h
-
spdlog/formatter.h
-
array
-
chrono
-
ctime
-
cctype
-
cstring
-
memory
-
mutex
-
string
-
thread
-
utility
-
vector
-

../../share/libs/x86/include/spdlog/pattern_formatter.h
spdlog/common.h
-
spdlog/details/log_msg.h
-
spdlog/details/os.h
-
spdlog/formatter.h
-
chrono
-
ctime
-
memory
-
string
-
vector
-
unordered_map
-
pattern_formatter-inl.h
../../share/libs/x86/include/spdlog/pattern_formatter-inl.h

../../share/libs/x86/include/spdlog/sinks/ansicolor_sink-inl.h
spdlog/sinks/ansicolor_sink.h
-
spdlog/pattern_formatter.h
-
spdlog/details/os.h
-

../../share/libs/x86/include/spdlog/sinks/ansicolor_sink.h
spdlog/details/console_globals.h
-
spdlog/details/null_mutex.h
-
spdlog/sinks/sink.h
-
memory
-
mutex
-
string
-
array
-
ansicolor_sink-inl.h
../../share/libs/x86/include/spdlog/sinks/ansicolor_sink-inl.h

../../share/libs/x86/include/spdlog/sinks/base_sink-inl.h
spdlog/sinks/base_sink.h
-
spdlog/common.h
-
spdlog/pattern_formatter.h
-
memory
-

../../share/libs/x86/include/spdlog/sinks/base_sink.h
spdlog/common.h
-
spdlog/details/log_msg.h
-
spdlog/sinks/sink.h
-
base_sink-inl.h
../../share/libs/x86/include/spdlog/sinks/base_sink-inl.h

../../share/libs/x86/include/spdlog/sinks/rotating_file_sink-inl.h
spdlog/sinks/rotating_file_sink.h
-
spdlog/common.h
-
spdlog/details/file_helper.h
-
spdlog/details/null_mutex.h
-
spdlog/fmt/fmt.h
-
cerrno
-
chrono
-
ctime
-
mutex
-
string
-
tuple
-

../../share/libs/x86/include/spdlog/sinks/rotating_file_sink.h
spdlog/sinks/base_sink.h
-
spdlog/details/file_helper.h
-
spdlog/details/null_mutex.h
-
spdlog/details/synchronous_factory.h
-
chrono
-
mutex
-
string
-
rotating_file_sink-inl.h
../../share/libs/x86/include/spdlog/sinks/rotating_file_sink-inl.h

../../share/libs/x86/include/spdlog/sinks/sink-inl.h
spdlog/sinks/sink.h
-
spdlog/common.h
-

../../share/libs/x86/include/spdlog/sinks/sink.h
spdlog/details/log_msg.h
-
spdlog/formatter.h
-
sink-inl.h
../../share/libs/x86/include/spdlog/sinks/sink-inl.h

../../share/libs/x86/include/spdlog/sinks/stdout_color_sinks-inl.h
spdlog/sinks/stdout_color_sinks.h
-
spdlog/logger.h
-
spdlog/common.h
-

../../share/libs/x86/include/spdlog/sinks/stdout_color_sinks.h
spdlog/sinks/wincolor_sink.h
-
spdlog/sinks/ansicolor_sink.h
-
spdlog/details/synchronous_factory.h
-
stdout_color_sinks-inl.h
../../share/libs/x86/include/spdlog/sinks/stdout_color_sinks-inl.h

../../share/libs/x86/include/spdlog/sinks/wincolor_sink-inl.h
spdlog/sinks/wincolor_sink.h
-
spdlog/common.h
-
spdlog/pattern_formatter.h
-

../../share/libs/x86/include/spdlog/sinks/wincolor_sink.h
spdlog/common.h
-
spdlog/details/console_globals.h
-
spdlog/details/null_mutex.h
-
spdlog/sinks/sink.h
-
memory
-
mutex
-
string
-
array
-
spdlog/details/windows_include.h
-
wincon.h
-
wincolor_sink-inl.h
../../share/libs/x86/include/spdlog/sinks/wincolor_sink-inl.h

../../share/libs/x86/include/spdlog/spdlog-inl.h
spdlog/spdlog.h
-
spdlog/common.h
-
spdlog/pattern_formatter.h
-

../../share/libs/x86/include/spdlog/spdlog.h
spdlog/common.h
-
spdlog/details/registry.h
-
spdlog/logger.h
-
spdlog/version.h
-
spdlog/details/synchronous_factory.h
-
chrono
-
functional
-
memory
-
string
-
spdlog-inl.h
../../share/libs/x86/include/spdlog/spdlog-inl.h

../../share/libs/x86/include/spdlog/tweakme.h

../../share/libs/x86/include/spdlog/version.h

/media/sf_work/auto_sort_high_efficient/camera_test/berxel_camera_test.cpp
berxel_camera_test.hpp
/media/sf_work/auto_sort_high_efficient/camera_test/berxel_camera_test.hpp
iostream
-
spdlog/spdlog.h
-
chrono
-
thread
-
iomanip
-
algorithm
-
cmath
-
arpa/inet.h
-

/media/sf_work/auto_sort_high_efficient/camera_test/berxel_camera_test.hpp
string
-
iostream
-
vector
-
memory
-
mutex
-
thread
-
functional
-
fstream
-
cmath
-
queue
-
condition_variable
-
atomic
-
map
-
BerxelHawkContext.h
/media/sf_work/auto_sort_high_efficient/camera_test/BerxelHawkContext.h
BerxelHawkDevice.h
/media/sf_work/auto_sort_high_efficient/camera_test/BerxelHawkDevice.h
BerxelHawkFrame.h
/media/sf_work/auto_sort_high_efficient/camera_test/BerxelHawkFrame.h
BerxelHawkDefines.h
/media/sf_work/auto_sort_high_efficient/camera_test/BerxelHawkDefines.h
stdio.h
-
stdlib.h
-
string.h
-
stdint.h
-
unistd.h
-
errno.h
-
spdlog/spdlog.h
-
spdlog/sinks/rotating_file_sink.h
-
spdlog/sinks/stdout_color_sinks.h
-

/media/sf_work/auto_sort_high_efficient/camera_test/main.cpp
berxel_camera_test.hpp
/media/sf_work/auto_sort_high_efficient/camera_test/berxel_camera_test.hpp
iostream
-
chrono
-
thread
-
signal.h
-
cmath
-
iomanip
-
fstream
-
sstream
-


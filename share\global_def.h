#ifndef __GLOBAL_DEF_H__
#define __GLOBAL_DEF_H__


#define REGISTER_SWELL              310


#define DATA_FILE_PATH 			    "/auto_sort_high_efficient/cfg_file/"
#define DATA_LOG_FILE_PATH			"/auto_sort_high_efficient/logs/data_base/"
#define THING_AGENT_LOG_FILE_PATH   "/auto_sort_high_efficient/logs/thing_agent/"
#define WCS_AGENT_LOG_FILE_PATH     "/auto_sort_high_efficient/logs/wcs_agent/"
#define LOG_FILE_PATH				"logs/"
#define MAP_FILE_NAME 				"new_map.json"
#define IRREGULAR_MAP_FILE_NAME		"map.json"
#define SCHEDULER_FILE_NAME         "scheduler.json"
#define THING_AGENT_FILE_NAME       "device.json"
#define WCS_AGENT_FILE_NAME         "wcs_config.json"
#define CONTAINER_FILE_NAME         "container_config.json"


#define SERVICE_DATA_ACCESS			        "tcp://127.0.0.1:8100"

#define TOPIC_SYS_STATE				        "ipc:///tmp/cc_high/sys/state"
#define TOPIC_SYS_CMD				        "ipc:///tmp/cc_high/sys/cmd"
#define TOPIC_SYS_RESET				        "ipc:///tmp/cc_high/sys/reset"
#define TOPIC_SYS_AUTO_EXCHANGE_CMD			"ipc:///tmp/cc_high/sys/exchange_cmd"


#define SERVICE_TASK				        "ipc:///tmp/cc_high/task/task"
#define TOPIC_TASK_STATE			        "ipc:///tmp/cc_high/task/task_state"

#define TOPIC_HMI_KEYEVT		            "ipc:///tmp/cc_high/hmi/key"
#define TOPIC_HMI_LEDCMD		            "ipc:///tmp/cc_high/hmi/led"

#define SERVICE_TRAIN_TASK		            "ipc:///tmp/cc_high/train/task"
#define TOPIC_TRAIN_PLATFORM_TASK_STATE	    "ipc:///tmp/cc_high/train/ptask_state"
#define TOPIC_TRAIN_CARRIAGE_TASK	        "ipc:///tmp/cc_high/train/ctask_state"
#define TOPIC_TRAIN_AGENT_STATE	            "ipc:///tmp/cc_high/train/agent_state"
#define TOPIC_TRAIN_STATE			        "ipc:///tmp/cc_high/train/state"
#define TOPIC_TRAIN_EXTSTATE		        "tcp://127.0.0.1:8201"
#define SERVICE_TARIN_BASE_INFO			    "tcp://127.0.0.1:8202"


#define TOPIC_SCHEDULER_STATE	            "ipc:///tmp/cc_high/sch/state"


#define SERVICE_FEEDER_CMD			        "ipc:///tmp/cc_high/feeder/cmd"
#define TOPIC_FEEDER_GOODS 			        "ipc:///tmp/cc_high/feeder/goods"
#define TOPIC_FEEDER_KEYEVT		            "ipc:///tmp/cc_high/feeder/key"
#define TOPIC_FEEDER_STATE		            "ipc:///tmp/cc_high/feeder/state"
#define SERVICE_FEEDER_TASK			        "ipc:///tmp/cc_high/feeder/task"
#define TOPIC_FEEDER_SUPPLY		            "ipc:///tmp/cc_high/feeder/su_state"
#define TOPIC_FEEDER_BARCODE 			    "ipc:///tmp/cc_high/feeder/barcode"


#define TOPIC_EXCEPTION				        "ipc:///tmp/cc_high/exception"
#define TOPIC_EXCEPTION_SCHED		        "ipc:///tmp/cc_high/exception/sched"
#define TOPIC_EXCEPTION_TRAIN		        "ipc:///tmp/cc_high/exception/train"
#define TOPIC_EXCEPTION_CONTAINER	        "ipc:///tmp/cc_high/exception/container"
#define TOPIC_EXCEPTION_CORE_SERVICE		"ipc:///tmp/cc_high/exception/core_service"
#define TOPIC_EXCEPTION_FEEDER		        "ipc:///tmp/cc_high/exception/feeder"
#define TOPIC_EXCEPTION_THING               "ipc:///tmp/cc_high/exception/thing"
#define TOPIC_EXCEPTION_AUTO_EXCHANGE       "ipc:///tmp/cc_high/exception/auto_exchange"
#define TOPIC_EXCEPTION_AUTO_EXCHANGE_SCH   "ipc:///tmp/cc_high/exception/autoexchange_sch"

#define TOPIC_CONTAINER_FULL_STATE 	        "ipc:///tmp/cc_high/container/full_state"          //格口满箱状态发布
#define TOPIC_CONTAINER_INFO		        "ipc:///tmp/cc_high/container/info"                //绑框/解绑状态发布
#define TOPIC_CONTAINER_SEAL_STATE          "ipc:///tmp/cc_high/container/seal_state"          //格口封箱状态发布
#define TOPIC_CONTAINER_ACT			        "ipc:///tmp/cc_high/container/act"                 //格口命令发布
#define TOPIC_CONTAINER_SEAL_CMD            "ipc:///tmp/cc_high/container/seal_cmd"            //封箱命令发布
#define TOPIC_CONTAINER_SEAL_SEND           "ipc:///tmp/cc_high/container/seal_send"           //受控封箱状态发布
#define TOPIC_CONTAINER_LOCK                "ipc:///tmp/cc_high/container/lock"                 //格口组锁定/解锁命令
#define TOPIC_CONTAINER_STATE	            "ipc:///tmp/cc_high/container/state"
#define TOPIC_SHELFS_LOCK_STATE             "ipc:///tmp/cc_high/container/shelf_state"
#define TOPIC_SHELFS_LOCK_CMD               "ipc:///tmp/cc_high/container/shelf_cmd"


#define SERVICE_AUTO_EXCHANGE_TASK		    "ipc:///tmp/cc_high/autoexchange/task"             //自动取换箱任务接口  调度->代理
#define TOPIC_AUTO_EXCHANGE_TASK_STATE	    "ipc:///tmp/cc_high/autoexchange/movetask_state"   //自动取换箱移动任务状态接口  代理->调度
#define TOPIC_AUTO_EXCHANGE_TASK	        "ipc:///tmp/cc_high/autoexchange/grabtask_state"   //自动取换箱抓取/卸货任务状态接口  代理->调度 
#define TOPIC_AUTO_EXCHANGE_AGENT_STATE	    "ipc:///tmp/cc_high/autoexchange/agent_state"      //自动取换箱代理运行状态接口  代理->调度
#define TOPIC_AUTO_EXCHANGE_STATE			"ipc:///tmp/cc_high/autoexchange/state"            //自动取换箱设备心跳状态接口  代理->调度

#define SERVICE_AUTO_EXCHANGE_SYS_TASK		"ipc:///tmp/cc_high/task/aotuexchangetask"         //自动取换箱上游任务获取接口  thing_agent->调度
#define TOPIC_AUTO_EXCHANGE_SYSTASK_STATE	"ipc:///tmp/cc_high/task/aotuexchangetask_state"   //自动取换箱上游任务状态上报接口 调度->thing_agent

#define TOPIC_AUTO_EXCHANGE_EXTSTATE		"tcp://127.0.0.1:8301"                             //自动取换箱上报设备状态接口  调度->thing_agent
#define SERVICE_AUTO_EXCHANGE_BASE_INFO		"tcp://127.0.0.1:8302"                             //自动取换箱获取固件信息接口  调度->代理

#define TOPIC_PACKAGE_STATION_STATE		    "ipc:///tmp/cc_high/packagestation/state"           //打包台设备状态接口  thing_agent -> 自动取换调度


#define DATA_KEY_MAP 				        "map"                                          //CoreService 提供
#define DATA_KEY_CONTAINER_INFO		        "container_info/"                              //CoreService 提供
#define DATA_KEY_LED_INFO		            "led_info_total/"                              //CoreService 提供
#define DATA_KEY_BOX_INFO		            "box_info_total/"                              //CoreService 提供
#define DATA_KEY_COORD_CONV				    "coord_conv"                                   //CoreService 提供


#define DATA_KEY_TRAIN_INFO                 "train_info"                                    //CoreService 提供
#define DATA_KEY_TRAIN_LIST		            "train_enable_list"                             //CoreService 提供
#define DATA_KEY_TRAIN_BASE_INFO            "vehicle_base_info"                             //train agent 提供
#define DATA_KEY_TRAIN_CFG_PARA             "train_cfg_para"                                //train agent 提供

#define DATA_KEY_AUTO_EXCHANGE_MAP	        "auto_exchange_map"                             //CoreService 提供
#define DATA_KEY_AUTO_EXCHANGE_BASE_INFO    "autoexchange_base_info"                        //自动取换箱代理提供
#define DATA_KEY_AUTO_EXCHANGE_OFFSET_INFO  "autoexchange_offset_info"                      //CoreService 提供
#define DATA_KEY_AUTO_EXCHANGE_LIST		    "autoexchange_enable_list"                      //CoreService 提供
#define DATA_KEY_PACKAGT_STATION_MAP	    "pack_station_map"                             //CoreService 提供
#define DATA_KEY_AUTO_EXCHANGE_COORD_CONV	"autoexchange_coord_conv"                        //CoreService 提供



#define SURESORT_HORIZONTAL_VERSION

#define CONTAINER_QUERY_FUNCTION_VERSION




#endif

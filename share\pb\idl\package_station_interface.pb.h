/* Automatically generated nanopb header */
/* Generated by nanopb-0.4.8 */

#ifndef PB_PACKAGE_STATION_INTERFACE_PB_H_INCLUDED
#define PB_PACKAGE_STATION_INTERFACE_PB_H_INCLUDED
#include <pb.h>
#include "sys_interface.pb.h"

#if PB_PROTO_HEADER_VERSION != 40
#error Regenerate this file with the current version of nanopb generator.
#endif

/* Enum definitions */
typedef enum _package_station_device_state {
    package_station_device_state_DEV_STATE_RESERVE = 0,
    package_station_device_state_DEV_STATE_INIT = 1,
    package_station_device_state_DEV_STATE_NORMAL = 2,
    package_station_device_state_DEV_STATE_ERROR = 3,
    package_station_device_state_DEV_STATE_FATAL = 4,
    package_station_device_state_DEV_STATE_EMERG_STOP = 5,
    package_station_device_state_DEV_STATE_UNKNOWN = 6
} package_station_device_state;

typedef enum _package_station_work_state {
    package_station_work_state_P_WORK_STATE_RESERVE = 0,
    package_station_work_state_P_WORK_STATE_INIT = 1,
    package_station_work_state_P_WORK_STATE_CHECK = 2,
    package_station_work_state_P_WORK_STATE_IDLE = 3,
    package_station_work_state_P_WORK_STATE_WORK = 4,
    package_station_work_state_P_WORK_STATE_ERROR = 5,
    package_station_work_state_P_WORK_STATE_FATAL = 6
} package_station_work_state;

/* Struct definitions */
typedef struct _package_station_dev_state {
    /* 设备基础信息 */
    uint32_t dev_id; /* 设备ID */
    package_station_device_state curr_state;
    package_station_work_state work_state; /* 设备工作状态 */
    int32_t dev_error_level; /* 异常等级 */
    uint32_t dev_error_no; /* 车头故障码 */
} package_station_dev_state;


#ifdef __cplusplus
extern "C" {
#endif

/* Helper constants for enums */
#define _package_station_device_state_MIN package_station_device_state_DEV_STATE_RESERVE
#define _package_station_device_state_MAX package_station_device_state_DEV_STATE_UNKNOWN
#define _package_station_device_state_ARRAYSIZE ((package_station_device_state)(package_station_device_state_DEV_STATE_UNKNOWN+1))

#define _package_station_work_state_MIN package_station_work_state_P_WORK_STATE_RESERVE
#define _package_station_work_state_MAX package_station_work_state_P_WORK_STATE_FATAL
#define _package_station_work_state_ARRAYSIZE ((package_station_work_state)(package_station_work_state_P_WORK_STATE_FATAL+1))

#define package_station_dev_state_curr_state_ENUMTYPE package_station_device_state
#define package_station_dev_state_work_state_ENUMTYPE package_station_work_state


/* Initializer values for message structs */
#define package_station_dev_state_init_default   {0, _package_station_device_state_MIN, _package_station_work_state_MIN, 0, 0}
#define package_station_dev_state_init_zero      {0, _package_station_device_state_MIN, _package_station_work_state_MIN, 0, 0}

/* Field tags (for use in manual encoding/decoding) */
#define package_station_dev_state_dev_id_tag     1
#define package_station_dev_state_curr_state_tag 2
#define package_station_dev_state_work_state_tag 3
#define package_station_dev_state_dev_error_level_tag 4
#define package_station_dev_state_dev_error_no_tag 5

/* Struct field encoding specification for nanopb */
#define package_station_dev_state_FIELDLIST(X, a) \
X(a, STATIC,   SINGULAR, UINT32,   dev_id,            1) \
X(a, STATIC,   SINGULAR, UENUM,    curr_state,        2) \
X(a, STATIC,   SINGULAR, UENUM,    work_state,        3) \
X(a, STATIC,   SINGULAR, INT32,    dev_error_level,   4) \
X(a, STATIC,   SINGULAR, UINT32,   dev_error_no,      5)
#define package_station_dev_state_CALLBACK NULL
#define package_station_dev_state_DEFAULT NULL

extern const pb_msgdesc_t package_station_dev_state_msg;

/* Defines for backwards compatibility with code written before nanopb-0.4.0 */
#define package_station_dev_state_fields &package_station_dev_state_msg

/* Maximum encoded size of messages (where known) */
#define PACKAGE_STATION_INTERFACE_PB_H_MAX_SIZE  package_station_dev_state_size
#define package_station_dev_state_size           27

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

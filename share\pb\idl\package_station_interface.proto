syntax = "proto3";
option optimize_for = LITE_RUNTIME;
import "nanopb.proto";
import "sys_interface.proto";

//// 设备相关数据流设计

enum package_station_device_state
{
    DEV_STATE_RESERVE = 0;
	DEV_STATE_INIT = 1;
    DEV_STATE_NORMAL = 2;
    DEV_STATE_ERROR = 3;
	DEV_STATE_FATAL = 4;
	DEV_STATE_EMERG_STOP = 5;
	DEV_STATE_UNKNOWN = 6;
}

enum package_station_work_state
{
    P_WORK_STATE_RESERVE = 0;
    P_WORK_STATE_INIT = 1;
    P_WORK_STATE_CHECK = 2;
	P_WORK_STATE_IDLE = 3;
	P_WORK_STATE_WORK = 4;
	P_WORK_STATE_ERROR = 5;
	P_WORK_STATE_FATAL = 6;
}

message package_station_dev_state 
{
	//设备基础信息	
	uint32 dev_id = 1;									//设备ID
	package_station_device_state curr_state = 2;
	package_station_work_state work_state = 3;			//设备工作状态
	int32 dev_error_level = 4;							//异常等级
	uint32 dev_error_no = 5;							//车头故障码
}



/**@file  	   device_manage.hpp
* @brief       本播墙调度系统的地图管理源代码
* @details     NULL
* <AUTHOR>
* @date        2024-10-10
* @version     v1.0.0
* @copyright   Copyright (c) 2050
**********************************************************************************
* @attention
* 主程序版本：v1.1.0
* @par 修改日志:
* <table>
* <tr><th>Date        <th>Version  <th>Author    <th>Description                  </tr>
* <tr><td>2024/10/10  <td>1.0.0    <td>lizhy     <td>初始版本	                   </tr>
* </table>
*
**********************************************************************************
*/




#ifndef __MAP_MANAGER_HPP__
#define __MAP_MANAGER_HPP__

//#include "../threadpool/blocking_queue.hpp"

#include "share/pb/nanopb/pb_encode.h"
#include "share/pb/nanopb/pb_decode.h"

#include "share/pb/idl/data_map.pb.h"
#include "share/pb/idl/data_request.pb.h"
#include "share/pb/idl/train_interface.pb.h"

#include <string>
#include <iostream>
#include <vector>
#include <map>
#include <functional>
#include <memory>
#include <unordered_map>
#include <list>
#include <zmq.h>
#include <cppzmq/zmq.hpp>

#include <thread>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <errno.h>
#include <stdexcept>
#include <mutex> 
#include <unordered_map>


using namespace std;


typedef struct __map_pos_lock
{
	int dev_id;
	int start;
	int following;
	int end;
	bool orig_flag;
}map_pos_lock;



typedef unordered_map<int, map_pos_lock> dev_global_locK_map;



/**
* @brief 根据JSON文件生成地图信息
*/
class map_manager
{
public:
	
	
	~map_manager();

	bool map_manager_init(zmq::context_t &ctx);
	void map_manager_run(); 

	int map_manager_get_sys_map(void);
	int map_manager_get_train_length(void);

	void map_manager_inner_service_thread();

	void map_manager_dev_self_pos_lock(int dev_id, int dev_pos);
	
	bool map_manager_dev_route_check(int dev_id, int dev_pos);
	int  map_manager_dev_route_apply(int dev_id, int curr_pos);

	int  map_manager_get_sys_feeder_cnt(void);
	bool map_manager_get_sys_feeder_info(int id, data_map_feeders_infomation &info);

	bool  map_manager_get_designated_container_info(int id, data_map_container_info &info);

	bool  map_manager_get_designated_camera_info(int id, data_map_gray_camera_info &info);

	data_map_containers_info map_manager_get_sys_container_info(void);

	train_para map_manager_get_sys_train_info(void);

	
	int map_manager_dev_route_apply(int dev_id, int expect_limit, int &target_pos, bool &ori_flag);


	int map_manager_get_sys_feeder_heigth(int id);

	int map_manager_get_sys_feeder_heigth(void);

	bool map_manager_find_nearest_feeder(int des, int &feeder_id, int &distance);

	int map_manager_fine_nearest_hos_container(int dev_pos );

	int map_manager_get_feeder_bind_hos_slot(int id);

	bool map_manager_is_curr_pos_camera_confirm_valid(int pos);

	bool map_manager_is_curr_pos_feeder_protect(int pos);

	float map_manager_get_sys_straight_len(void);

	int map_manager_get_feeder_base_camera_id(int camera_id);

	float map_manager_get_dev_total_len(void);

	bool  map_manager_is_curr_pos_feeder_avoid_valid(int pos);


	int map_manager_get_coord_conv_para(void);

	position_xyz map_manager_coord_conv(int mileage, int y_axis);

	int map_manager_get_top_container_height(void);

	bool map_manager_if_device_in_feeder_range(int pos, int &feeder_id);


	int  map_manager_get_dev_total_height(void);

	data_map_tunnel_type map_manager_curr_pos_type(int pos);

	int map_manager_curr_pos_arc_distance(int pos);

	int map_manager_curr_pos_nearest_arc_distance(int pos);

	int map_manager_curr_pos_nearest_arc_pos(int pos);

	float map_manager_get_sys_arc_len(void);


	static map_manager *get_instance(void)
    { 
        static map_manager instance;
        return &instance;
    }
	
private:

	//std::mutex m_map_apply_lock;

	data_map m_dev_map;
	train_para m_train_para;

	coordinate_conv_para m_coord_conv_para;


	int m_global_route;

	std::mutex m_map_opt_lock;
	dev_global_locK_map m_dev_map_lock;

	int m_route_total_len;
	int m_following_dis;
	int m_apply_max_len;
	int m_apply_max_len_calib;

	int m_conr_conv_x_offset;
	int m_conr_conv_y_offset;
	int m_conr_conv_z_offset;




};





#endif
